export  async function fetchQuotes  (domElementSelector) {
    let mainTitle = document.querySelector(domElementSelector);
    
    // Vérifie si l'URL est celle de la page d'accueil
    if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
        // if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {

        //Data va récup Toutes les données de l'api de <PERSON>
        const response = await fetch('https://api.chucknorris.io/jokes/random');
        console.log(response);
        console.log(response.ok);
        console.log(response.status);
        //Plutôt que de Travailler sur la réponse, on va la transformé en objet JS 
        const dataTransformed = await response.json();
        console.log(dataTransformed);
        mainTitle.innerText = dataTransformed.value; // Affiche la blague
    }
    // SALE NOOB ya aucune Gestion d'erreur JEff (note a soi meme)
};