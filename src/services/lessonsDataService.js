/**
 * Configuration des leçons disponibles
 * Chaque leçon contient les informations nécessaires pour générer sa carte
 */
export const lessonsData = [
    {
        id: 'history',
        title: 'L\'Histoire de JavaScript',
        description: 'Découvrez l\'histoire de JavaScript et son impact sur le développement web.',
        difficulty: 'Débutant',
        duration: '30 min',
        topics: ['Histoire', 'JavaScript'],
        file: 'history.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 0
    },
    {
        id: 'setup',
        title: 'Configuration & Setup',
        description: 'Apprenez à configurer votre environnement de développement JavaScript et les outils essentiels.',
        difficulty: 'Débutant',
        duration: '30 min',
        topics: ['Setup', 'Outils', 'Configuration'],
        file: 'setup.html',
        status: 'available',
        badge: 'Essentiel',
        category: 'Fondamentaux',
        order: 1
    },
    {
        id: 'variables',
        title: 'Variables JavaScript',
        description: 'Découvrez les différents types de variables en JavaScript : var, let, const et leurs portées.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Variables', 'Portée', 'Types'],
        file: 'variables.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 2
    },
    {
        id: 'Numbers',
        title: 'Numbers JavaScript',
        description: 'Découvrez les différents types de nombres en JavaScript : entiers, flottants, NaN et Infinity.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Nombres', 'Types'],
        file: 'numbers.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 3
    },
    {
        id: 'Strings',
        title: 'Strings JavaScript',
        description: 'Découvrez comment utiliser les chaînes de caractères en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'strings.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 4
    },
    {
        id: 'Arrays',
        title: 'Arrays JavaScript',
        description: 'Découvrez comment utiliser les tableaux en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'arrays.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 5
    },
    {
        id: 'Functions',
        title: 'Les fonctions en JavaScript',
        description: 'Apprenez à créer, appeler et comprendre les fonctions en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'functions.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 6
    },
    {
        id: 'Operators',
        title: 'Les opérateurs en JavaScript',
        description: 'Apprenez à utiliser les opérateurs en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'operators.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 7
    },
    {
        id: 'Conditions',
        title: 'Les conditions en JavaScript',
        description: 'Apprenez à utiliser les conditions en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'conditions.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 8
    },
    {
        id: 'Objects',
        title: 'Les objets en JavaScript',
        description: 'Apprenez à utiliser les objets en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'objects.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 9
    },
    {
        id: 'Boucles',
        title: 'Les boucles en JavaScript',
        description: 'Apprenez à utiliser les boucles en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'loops.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 10
    },
    {
        id: 'Spread Operator',
        title: 'Les opérateurs de propagation en JavaScript',
        description: 'Apprenez à utiliser les opérateurs de propagation en JavaScript.',
        difficulty: 'Avancé',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'spread-operator.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 11
    },
    {
        id: 'Gestion des erreurs',
        title: 'La gestion des erreurs en JavaScript',
        description: 'Apprenez à gérer les erreurs en JavaScript.',
        difficulty: 'Intermédiaire',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'errors.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 12
    },
    {
        id: 'dom-selectors',
        title: 'Sélecteurs DOM',
        description: 'Maîtrisez les différentes méthodes pour sélectionner et manipuler les éléments du DOM.',
        difficulty: 'Débutant',
        duration: '50 min',
        topics: ['DOM', 'Sélecteurs', 'Manipulation'],
        file: 'dom-selectors.html',
        status: 'available',
        badge: 'Populaire',
        category: 'DOM',
        order: 13
    },
    {
        id: 'dom-attributes',
        title: 'Modifier les attributs HTML & CSS en JavaScript',
        description: 'Apprenez à modifier les attributs HTML et les styles CSS des éléments du DOM.',
        difficulty: 'Intermédiaire',
        duration: '50 min',
        topics: ['DOM', 'Attributs', 'Styles'],
        file: 'dom-attributes.html',
        status: 'available',
        badge: 'Populaire',
        category: 'DOM',
        order: 14
    },
    {
        id: 'dom-events',
        title: 'Événements DOM',
        description: 'Apprenez à gérer les événements utilisateur et à créer des interactions dynamiques.',
        difficulty: 'Intermédiaire',
        duration: '60 min',
        topics: ['DOM', 'Événements', 'Interactions'],
        file: 'dom-events.html',
        status: 'available',
        badge: 'Interactif',
        category: 'DOM',
        order: 15
    },
    {
        id: 'web storage',
        title: 'Web Storage : localStorage, sessionStorage et Cookies',
        description: 'Apprenez à utiliser les différents moyens de stocker des données dans le navigateur.',
        difficulty: 'Avancé',
        duration: '60 min',
        topics: ['Web Storage', 'localStorage', 'sessionStorage', 'Cookies'],
        file: 'web-storage.html',
        status: 'available',
        badge: 'Interactif',
        category: 'DOM',
        order: 16
    },
    {
        id: 'regexp',
        title: 'Expressions régulières en JavaScript',
        description: 'Apprenez à utiliser les expressions régulières pour manipuler des chaînes de caractères.',
        difficulty: 'Intermédiaire',
        duration: '55 min',
        topics: ['Expressions régulières', 'Regex', 'Méthodes'],
        file: 'regexp.html',
        status: 'available',
        badge: null,
        category: 'Fondamentaux',
        order: 17
    },
    {
        id: 'libs',
        title: 'Bibliothèques JavaScript',
        description: 'Introduction aux bibliothèques populaires et comment les intégrer dans vos projets.',
        difficulty: 'Intermédiaire',
        duration: '55 min',
        topics: ['Bibliothèques', 'NPM', 'Modules'],
        file: 'libs.html',
        status: 'available',
        badge: null,
        category: 'Outils',
        order: 18
    },
    {
        id: 'api',
        title: 'API et Fetch en JavaScript',
        description: 'Apprenez à utiliser les API et la fonction Fetch pour récupérer des données depuis des serveurs externes.',
        difficulty: 'Intermédiaire',
        duration: '55 min',
        topics: ['Fetch', 'API', 'Async / Await'],
        file: 'api.html',
        status: 'available',
        badge: null,
        category: 'Fondamentaux',
        order: 19
    },
    {
        id: 'modules',
        title: 'Modules JavaScript',
        description: 'Apprenez à utiliser les modules JavaScript pour organiser votre code.',
        difficulty: 'Intermédiaire',
        duration: '55 min',
        topics: ['Modules', 'Import', 'Export'],
        file: 'modules.html',
        status: 'available',
        badge: null,
        category: 'Fondamentaux',
        order: 20
    },
    {
        id: 'variables-env',
        title: 'Variables d\'Environnement',
        description: 'Apprenez à utiliser les variables d\'environnement avec Vite et à sécuriser vos configurations.',
        difficulty: 'Intermédiaire',
        duration: '35 min',
        topics: ['Env', 'Vite', 'Configuration'],
        file: 'variables-env.html',
        status: 'available',
        badge: null,
        category: 'Configuration',
        order: 21
    },
    {
        id: 'Les classes',
        title: 'Les classes en JavaScript',
        description: 'Apprenez à utiliser les classes en JavaScript.',
        difficulty: 'Intermédiaire',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'classes.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 22
    },
    {
        id: 'Les Web Workers',
        title: 'Les Web Workers en JavaScript',
        description: 'Apprenez à utiliser les Web Workers en JavaScript.',
        difficulty: 'Avancé',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'web-workers.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 23
    },
    {
        id: 'Les Custom Elements',
        title: 'Les Custom Elements en JavaScript',
        description: 'Apprenez à utiliser les Custom Elements en JavaScript.',
        difficulty: 'Avancé',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'custom-elements.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 24
    },
    {
        id: 'Intro Typescript',
        title: 'Introduction à TypeScript',
        description: 'Apprenez à utiliser TypeScript pour développer des applications TypeScript.',
        difficulty: 'Avancé',
        duration: '45 min',
        topics: ['Strings', 'Types'],
        file: 'history-ts.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 25
    },
];
