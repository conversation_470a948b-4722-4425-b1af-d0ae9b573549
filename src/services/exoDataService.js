/**
 * Configuration des exercices disponibles
 * Chaque exercice contient les informations nécessaires pour générer sa carte
 */
export const exercicesData = [
    {
        id: 'variables',
        title: 'Exercices Variables',
        description: 'Exercices pratiques sur la déclaration, l\'affectation et la manipulation des variables JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Variables', 'Types', 'Portée'],
        file: 'variables.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 1,
        exerciseType: 'pratique'
    },
    {
        id: 'calculs',
        title: 'Exercices Calculs',
        description: 'Exercices pratiques sur les opérations mathématiques en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Calculs', 'Opérateurs'],
        file: 'calculs.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 2,
        exerciseType: 'pratique'
    },
    {
        id: 'strings',
        title: 'Exercices Strings',
        description: 'Exercices pratiques sur les chnsdee de caractères en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Calculs', 'Opérateurs'],
        file: 'strings.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 3,
        exerciseType: 'pratique'
    },
    {
        id: 'arrays',
        title: 'Exercices Tableaux',
        description: 'Exercices pratiques sur les tableaux en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Calculs', 'Opérateurs'],
        file: 'arrays.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 4,
        exerciseType: 'pratique'
    },
    {
        id: 'functions',
        title: 'Exercices Fonctions',
        description: 'Exercices pratiques sur les fonctions en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Calculs', 'Opérateurs'],
        file: 'functions.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 5,
        exerciseType: 'pratique'
    },

    {
        id: 'conditions',
        title: 'Exercices Conditions',
        description: 'Exercices pratiques sur les condtions en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Calculs', 'Opérateurs'],
        file: 'conditions.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 6,
        exerciseType: 'pratique'
    },
    {
        id: 'objects',
        title: 'Exercices Objets',
        description: 'Exercices pratiques sur les objets en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Objets', 'Tableaux','Boucles'],
        file: 'objects.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 7,
        exerciseType: 'pratique'
    },
    {
        id: 'loops',
        title: 'Exercices Boucles',
        description: 'Exercices pratiques sur les boucles en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Objets', 'Tableaux','Boucles'],
        file: 'loops.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 8,
        exerciseType: 'pratique'
    },
    {
        id: 'scope',
        title: 'Exercices Scope',
        description: 'Exercices pratiques sur les scope en JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: [ 'scope', 'portée','var let const'],
        file: 'scope.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 9,
        exerciseType: 'pratique'
    },
    {
        id: 'classes',
        title: 'Exercices Classes',
        description: 'Exercices pratiques sur les classes en JavaScript.',
        difficulty: 'Avancé',
        duration: '25 min',
        topics: [ 'classes', 'objets','constructeur'],
        file: 'classes.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 10,
        exerciseType: 'pratique'
    },
    {
        id: 'API',
        title: 'Exercices API',
        description: 'Exercices pratiques sur les API en JavaScript.',
        difficulty: 'Intermédiaire',
        duration: '25 min',
        topics: [ 'API', 'fetch','JSON'],
        file: 'api.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 11,
        exerciseType: 'pratique'
    },
    {
        id: 'dom-selectors',
        title: 'Exercices Sélecteurs DOM',
        description: 'Maîtrisez les sélecteurs DOM avec des exercices pratiques sur querySelector, getElementById et plus.',
        difficulty: 'Débutant',
        duration: '35 min',
        topics: ['DOM', 'Sélecteurs', 'Manipulation'],
        file: 'dom-selectors.html',
        status: 'available',
        badge: null,
        category: 'DOM',
        order: 12,
        exerciseType: 'pratique'
    },
    {
        id: 'dom-events',
        title: 'Exercices DOM Events',
        description: 'Pratiquez la gestion des événements DOM avec des exercices interactifs sur les clics, saisie clavier et formulaires.',
        difficulty: 'Intermédiaire',
        duration: '30 min',
        topics: ['DOM', 'Événements', 'Interactions'],
        file: 'dom-events.html',
        status: 'available',
        badge: 'Interactif',
        category: 'DOM',
        order: 99,
        exerciseType: 'pratique'
    },

    {
        id: 'editeur-texte',
        title: 'Éditeur de Texte',
        description: 'Créez un éditeur de texte simple avec sauvegarde automatique et fonctionnalités avancées.',
        difficulty: 'Intermédiaire',
        duration: '60 min',
        topics: ['DOM', 'LocalStorage', 'Éditeur'],
        file: 'editeur-texte.html',
        status: 'available',
        badge: 'Nouveau',
        category: 'Projets',
        order: 99,
        exerciseType: 'projet'
    }
];