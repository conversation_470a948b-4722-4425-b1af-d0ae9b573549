import { competencesData } from './skillsDataService.js';
export function generateTableSkillsHTML(data) {
    let tableHTML = `
        <table class="table table-zebra w-full shadow-xl rounded-xl">
            <thead class="bg-primary text-primary-content">
                <tr>
                    <th class="w-16">N° Fiche AT</th>
                    <th>Activités types</th>
                    <th class="w-16">N° Fiche CP</th>
                    <th>Compétences professionnelles</th>
                    <th>Liens</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.forEach(atBlock => {
        atBlock.cps.forEach((cp, index) => {
            const rowspan = atBlock.cps.length;
            const isFirstRow = index === 0;

            // Crée le HTML pour les liens de chaque CP
            const linksHtml = cp.links.map(link => `
                <a href="${link.url}" class="badge badge-${atBlock.color} badge-outline hover:bg-${atBlock.color} hover:text-${atBlock.color}-content">${link.label}</a>
            `).join('');

            tableHTML += `
                <tr class="hover">
                    ${isFirstRow ? `
                        <td rowspan="${rowspan}" class="align-top text-center font-bold bg-${atBlock.color}/10">${atBlock.at}</td>
                        <td rowspan="${rowspan}" class="align-top font-semibold bg-${atBlock.color}/10">${atBlock.activite}</td>
                    ` : ''}
                    <td>
                        <div class="badge badge-${atBlock.color}">${cp.num}</div>
                    </td>
                    <td>${cp.text}</td>
                    <td>
                        <div class="flex flex-wrap gap-1">
                            ${linksHtml}
                        </div>
                    </td>
                </tr>
            `;
        });
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    return tableHTML;
}