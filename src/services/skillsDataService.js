export const competencesData = [
    {
        at: 1,
        activite: "Développer une application sécurisée",
        color: "primary",
        cps: [
            { num: 1, text: "Installer et configurer son environnement de travail en fonction du projet", links: [{ label: "Lesson 1", url: "#" }, { label: "Exo 1", url: "#" }] },
            { num: 2, text: "Développer des interfaces utilisateur", links: [{ label: "Lesson 2", url: "#" }, { label: "Exo 2", url: "#" }] },
            { num: 3, text: "Développer des composants métier", links: [{ label: "Lesson 3", url: "#" }] },
            { num: 4, text: "Contribuer à la gestion d’un projet informatique", links: [{ label: "Lesson 4", url: "#" }] }
        ]
    },
    {
        at: 2,
        activite: "Concevoir et développer une application sécurisée organisée en couches",
        color: "secondary",
        cps: [
            { num: 5, text: "Analyser les besoins et maqueter une application", links: [{ label: "Lesson 5", url: "#" }] },
            { num: 6, text: "Définir l’architecture logicielle d’une application", links: [{ label: "Lesson 6", url: "#" }] },
            { num: 7, text: "Concevoir et mettre en place une base de données relationnelle", links: [{ label: "Lesson 7", url: "#" }, { label: "Exo 3", url: "#" }] },
            { num: 8, text: "Développer des composants d’accès aux données SQL et NoSQL", links: [{ label: "Lesson 8", url: "#" }] }
        ]
    },
    {
        at: 3,
        activite: "Préparer le déploiement d’une application sécurisée",
        color: "accent",
        cps: [
            { num: 9, text: "Préparer et exécuter les plans de tests d’une application", links: [{ label: "Lesson 9", url: "#" }] },
            { num: 10, text: "Préparer et documenter le déploiement d’une application", links: [{ label: "Lesson 10", url: "#" }] },
            { num: 11, text: "Contribuer à la mise en production dans une démarche DevOps", links: [{ label: "Lesson 11", url: "#" }] }
        ]
    }
];