// src/services/timelineData.js
export const timelineSteps = [
    {
        year: "31/07/2025",
        title: "Évaluation JavaScript",
        text: "Évaluation à chaud pour tester le niveau au global.",
        side: "start" // start = gauche, end = droite
    },
    {
        year: "01/09/2025",
        title: "Introduction de JavaScript",
        text: "Introduction du contexte de création du langage Javascript (), intro du PDF, YT Deno vs Oracle,article <PERSON>, stateofjs, intro VITE, setup VITE npm, briefing structure projet, Console Ninja, lesson Variables, Exo Variables, lesson DOM Selector, lesson DOM placement, lesson DOM creation elements, TP UserProfile",
        side: "end" // start = gauche, end = droite
    },
    {
        year: "02/09/2025",
        title: "L'aventure continue en JavaScript",
        text: "Appropriation du Projet, nouvelle organisation des fichier et dossier du projet, intro librairies, installation et configuration de tailwind + DaisyUI, rev <PERSON>, Exo DOM Creation Manip + Objet, fonctions, Exo DOM Manip : nom du site dynamique (navbar), date footer dynamique, JsDoc (l'art du commentaire), Rev fonction syntaxe (param, return anonyme, fléchées), intro addEventListener,Exo Dom addEventListener (click change titre), event keyup, Exo Dom keyup, intro localStorage, Exo Dom keyup + localstorage.",
        side: "start" // start = gauche, end = droite
    },
    {
        year: "03/09/2025",
        title: "Ambiance studieuse en JavaScript",
        text: "Exo DOM keyup + localStorage, intro librairies, Intro variable env, TP Compteur mensonge persistant, Rev Loops, TP Group Page Team Automatisée..",
        side: "end" // start = gauche, end = droite
    },
    {
        year: "04/09/2025",
        title: "On Prend du level en Javascript",
        text: "Gestion des erreurs, Javascript modulaire (import export),Exo Timeline double services, les API avec fetch, exo API chuck Norris + service les classes JS)",
        side: "start"
    },
    {
        year: "05/09/2025",
        title: "On est en asynchrone Télé-travail en Javascript",
        text: "Continuité cours sur les classes , des Exercices sur les classes, cours sur les regexp, exercice Regexp, introduction typescript. ",
        side: "end"
    }
];
