/**
 * Configuration des TPs disponibles
 * Chaque TP contient les informations nécessaires pour générer sa carte
 */
export const tpData = [
    {
        id: 'mensonge',
        title: 'TP : Mensonge',
        description: 'Travaux pratiques sur la manipulation des variables et la logique conditionnelle en JavaScript.',
        difficulty: 'Intermédiaire',
        duration: '45 min',
        topics: ['Variables', 'Conditions', 'Logique'],
        file: 'mensonge.html',
        status: 'available', // available, coming-soon, completed
        badge: 'Nouveau'
    },
    {
        id: 'user-profile',
        title: 'TP : Profil Utilisateur - API',
        description: 'Création d\'un système de profil utilisateur avec manipulation du DOM et gestion des événements.',
        difficulty: 'Intermédiaire',
        duration: '90 min',
        topics: ['DOM', 'Événements', 'API'],
        file: 'user-profile.html',
        status: 'available',
        badge: null
    },
    {
        id: 'calculator',
        title: 'TP : Calculatrice',
        description: 'Développement d\'une calculatrice interactive avec gestion des opérations mathématiques.',
        difficulty: 'Intermédiaire',
        duration: '120 min',
        topics: ['Fonctions', 'Événements', 'Math'],
        file: 'calculator.html',
        status: 'available',
        badge: null
    },
    {
        id: 'social-network',
        title: 'TP : Réseau Social',
        description: 'Développement d\'un réseau social avec gestion des utilisateurs et des publications.',
        difficulty: 'Intermédiaire',
        duration: '120 min',
        topics: ['Objets', 'Classes'],
        file: 'social-network.html',
        status: 'available',
        badge: null
    },
    {
        id: 'tp-group-team',
        title: 'TP Groupe : page Team Dynamqique',
        description: 'Développement d\'une page de groupe dynamique avec gestion des utilisateurs et des publications.',
        difficulty: 'Intermédiaire',
        duration: '120 min',
        topics: ['Objets', 'Classes', 'DOM'],
        file: '/../../team.html',
        status: 'available',
        badge: null
    },
    {
        id: 'DOM API Meteo',
        title: 'TP : DOM API Meteo',
        description: 'Création d\'une application de météo avec manipulation du DOM.',
        difficulty: 'Avancé',
        duration: '150 min',
        topics: ['LocalStorage', 'CRUD', 'DOM'],
        file: 'meteo.html',
        status: 'available',
        badge: 'Nouveau'
    },
    {
        id: 'Classes Compte Bancaires',
        title: 'TP : Classes Compte Bancaires',
        description: 'Création d\'une application de gestion de compte bancaire avec manipulation du DOM.',
        difficulty: 'Avancé',
        duration: '150 min',
        topics: ['LocalStorage', 'CRUD', 'DOM'],
        file: 'bank.html',
        status: 'available',
        badge: 'Nouveau'
    },
    {
        id: 'todo-list',
        title: 'TP : Todo List',
        description: 'Création d\'une application de gestion de tâches avec localStorage et manipulation du DOM.',
        difficulty: 'Avancé',
        duration: '150 min',
        topics: ['LocalStorage', 'CRUD', 'DOM'],
        file: 'todo-list.html',
        status: 'available',
        badge: 'Nouveau'
    }
];