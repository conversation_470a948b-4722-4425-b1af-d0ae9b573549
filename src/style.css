@import "tailwindcss";
@plugin "daisyui";

/* Animations pour les cards équipe */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
  opacity: 0;
}

/* Styles pour les cards d'équipe */
.team-card {
  transition: all 0.3s ease;
}

.team-card:hover {
  transform: translateY(-5px);
}

/* Limitation du nombre de lignes pour la bio */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation pour les badges de compétences */
.badge {
  transition: all 0.2s ease;
}

.badge:hover {
  transform: scale(1.05);
}

/* Styles pour la modal */
.modal-box {
  max-height: 90vh;
  overflow-y: auto;
}

/* Animation pour les statistiques */
.stats .stat {
  transition: all 0.3s ease;
}

.stats .stat:hover {
  transform: scale(1.02);
  background-color: hsl(var(--b2));
}

/* Styles pour la page de leçon DOM Events */
.lesson-section {
  transition: all 0.3s ease;
}

.lesson-section:hover {
  transform: translateY(-2px);
}

/* Animation pour les éléments de code */
.mockup-code {
  transition: all 0.3s ease;
}

.mockup-code:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* Styles pour les badges de code */
code.badge {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

/* Animation pour les boutons de quiz */
.quiz-option {
  transition: all 0.2s ease;
}

.quiz-option:hover {
  transform: translateX(5px);
}

/* Effet de pulsation pour les éléments interactifs */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

.ring-pulse {
  animation: pulse-ring 0.5s ease-out;
}

/* Styles pour les logs d'événements */
.event-log-container {
  transition: all 0.3s ease;
}

.event-log-container.highlight {
  background-color: hsl(var(--p) / 0.1);
  border: 2px solid hsl(var(--p));
}

/* Amélioration des cards de comparaison */
.comparison-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.comparison-card:hover {
  border-color: hsl(var(--p));
  transform: scale(1.02);
}

/*! ⬇️ Ancien CSS de base pour l'appli de counter mais pas le temps donc on fera DaisyUI basé sur Tailwind */
/* :root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vanilla:hover {
  filter: drop-shadow(0 0 2em #f7df1eaa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */
