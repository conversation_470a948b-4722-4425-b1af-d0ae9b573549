<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar Container - sera rempli par JavaScript -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <!-- <div class="max-w-4xl mx-auto"> -->
                    <!-- Header -->
                    <div class="text-center mb-8">
                        <h1 class="text-4xl font-bold text-primary mb-4">Politique de Confidentialité</h1>
                        <p class="text-lg text-base-content/70">Protection de vos données personnelles</p>
                    </div>

                    <!-- Content Card -->
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <!-- Introduction -->
                            <section class="mb-8">
                                <div class="alert alert-info mb-6">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <h3 class="font-bold">Bonne nouvelle !</h3>
                                        <div class="text-sm">Ce site ne collecte aucune donnée personnelle. Votre vie
                                            privée est totalement respectée.</div>
                                    </div>
                                </div>
                                <p class="text-base-content/80">
                                    Jefff303.js est un site éducatif conçu pour l'apprentissage du JavaScript.
                                    Nous nous engageons à protéger votre vie privée en ne collectant aucune information
                                    personnelle.
                                </p>
                            </section>

                            <!-- Données collectées -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h.01M15 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Données collectées
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <div class="alert alert-success mb-4">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span><strong>Aucune donnée personnelle n'est collectée</strong></span>
                                    </div>
                                    <ul class="space-y-2">
                                        <li class="flex items-center">
                                            <svg class="w-5 h-5 text-error mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Pas d'adresse email
                                        </li>
                                        <li class="flex items-center">
                                            <svg class="w-5 h-5 text-error mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Pas de nom ou prénom
                                        </li>
                                        <li class="flex items-center">
                                            <svg class="w-5 h-5 text-error mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Pas d'adresse IP stockée
                                        </li>
                                        <li class="flex items-center">
                                            <svg class="w-5 h-5 text-error mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                            Pas de données de navigation
                                        </li>
                                    </ul>
                                </div>
                            </section>

                            <!-- Cookies -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4">
                                        </path>
                                    </svg>
                                    Cookies et stockage local
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg space-y-4">
                                    <div class="alert alert-warning">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-bold">Stockage local uniquement</h3>
                                            <div class="text-sm">Seul le stockage local de votre navigateur peut être
                                                utilisé pour améliorer votre expérience.</div>
                                        </div>
                                    </div>
                                    <div class="space-y-3">
                                        <h3 class="font-semibold">Ce qui peut être stocké localement :</h3>
                                        <ul class="list-disc list-inside space-y-1 ml-4">
                                            <li>Votre progression dans les cours</li>
                                            <li>Vos préférences de thème (clair/sombre)</li>
                                            <li>L'état de vos exercices en cours</li>
                                            <li>Vos notes personnelles sur les leçons</li>
                                        </ul>
                                        <div class="alert alert-info mt-4">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                                </path>
                                            </svg>
                                            <span class="text-sm">Ces données restent sur votre appareil et ne sont
                                                jamais transmises à nos serveurs.</span>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Services tiers -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                    </svg>
                                    Services tiers
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <div class="space-y-4">
                                        <div class="card bg-base-100 p-4">
                                            <h3 class="font-semibold mb-2 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                                                </svg>
                                                GitHub Pages
                                            </h3>
                                            <p class="text-sm text-base-content/70">
                                                Ce site est hébergé sur GitHub Pages. GitHub peut collecter des données
                                                de base
                                                pour des raisons de sécurité et de performance selon leur propre
                                                politique de confidentialité.
                                            </p>
                                            <a href="https://docs.github.com/en/site-policy/privacy-policies/github-privacy-statement"
                                                target="_blank" class="link link-primary text-sm mt-2 inline-block">
                                                Voir la politique de GitHub →
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Vos droits -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                        </path>
                                    </svg>
                                    Vos droits
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <div class="alert alert-success mb-4">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>Puisque nous ne collectons aucune donnée, vous n'avez aucune démarche à
                                            effectuer !</span>
                                    </div>
                                    <div class="grid md:grid-cols-2 gap-4">
                                        <div class="card bg-base-100 p-4">
                                            <h3 class="font-semibold mb-2 text-success">✓ Contrôle total</h3>
                                            <p class="text-sm text-base-content/70">
                                                Vous gardez le contrôle total de vos données locales.
                                                Vous pouvez les effacer à tout moment via les paramètres de votre
                                                navigateur.
                                            </p>
                                        </div>
                                        <div class="card bg-base-100 p-4">
                                            <h3 class="font-semibold mb-2 text-success">✓ Transparence</h3>
                                            <p class="text-sm text-base-content/70">
                                                Le code source de ce site est open source et disponible sur GitHub.
                                                Vous pouvez vérifier par vous-même qu'aucune donnée n'est collectée.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Sécurité -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                        </path>
                                    </svg>
                                    Sécurité
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg space-y-4">
                                    <p class="text-base-content/80">
                                        Bien que nous ne collections aucune donnée, nous prenons la sécurité au sérieux
                                        :
                                    </p>
                                    <div class="grid md:grid-cols-3 gap-4">
                                        <div class="card bg-base-100 p-4 text-center">
                                            <svg class="w-8 h-8 mx-auto mb-2 text-success" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                                </path>
                                            </svg>
                                            <h3 class="font-semibold mb-1">HTTPS</h3>
                                            <p class="text-xs text-base-content/70">Connexion sécurisée</p>
                                        </div>
                                        <div class="card bg-base-100 p-4 text-center">
                                            <svg class="w-8 h-8 mx-auto mb-2 text-success" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                            </svg>
                                            <h3 class="font-semibold mb-1">Open Source</h3>
                                            <p class="text-xs text-base-content/70">Code transparent</p>
                                        </div>
                                        <div class="card bg-base-100 p-4 text-center">
                                            <svg class="w-8 h-8 mx-auto mb-2 text-success" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4">
                                                </path>
                                            </svg>
                                            <h3 class="font-semibold mb-1">Statique</h3>
                                            <p class="text-xs text-base-content/70">Pas de serveur</p>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Modifications -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Modifications de cette politique
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <p class="mb-3">
                                        Cette politique de confidentialité peut être mise à jour occasionnellement pour
                                        refléter
                                        les changements dans nos pratiques ou pour d'autres raisons opérationnelles,
                                        légales ou réglementaires.
                                    </p>
                                    <div class="alert alert-info">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-bold">Engagement</h3>
                                            <div class="text-sm">Nous nous engageons à maintenir notre approche de
                                                non-collecte de données personnelles.</div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Contact -->
                            <section>
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                    Questions sur la confidentialité
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <p class="mb-4">
                                        Si vous avez des questions concernant cette politique de confidentialité ou nos
                                        pratiques de protection des données :
                                    </p>
                                    <div class="flex flex-col sm:flex-row gap-4">
                                        <a href="mailto:<EMAIL>" class="btn btn-primary btn-outline">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                                </path>
                                            </svg>
                                            Nous contacter
                                        </a>
                                        <a href="https://github.com/jefff303js" target="_blank"
                                            class="btn btn-secondary btn-outline">
                                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                                <path
                                                    d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                                            </svg>
                                            Voir le code source
                                        </a>
                                    </div>
                                </div>
                            </section>

                            <!-- Date de mise à jour -->
                            <div class="text-center mt-8 pt-6 border-t border-base-300">
                                <p class="text-sm text-base-content/60">
                                    Dernière mise à jour : <span class="font-medium">Décembre 2024</span>
                                </p>
                            </div>
                        </div>
                    </div>
                <!-- </div> -->
            </main>

            <!-- Footer Container - sera rempli par JavaScript -->
            <div id="footer-container"></div>
        </div>
    </div>
    <!-- Pb ptet a adapter si page sous dossier/src/pages/exo/dom-events.html -->
    <script type="module" src="/src/main.js"></script>
</body>

</html>