<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar Container - sera rempli par JavaScript -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <!-- Le contenu spécifique de la leçon sera inséré ici -->
                <h1 class="text-4xl font-bold mb-6 text-center">Tableau de compétences</h1>
                <div id="table-container">
                    <!-- Le tableau sera généré ici par JavaScript -->
                </div>
            </main>



            <!-- Footer Container - sera rempli par JavaScript -->
            <div id="footer-container"></div>
        </div>
    </div>
    <!-- Pb ptet a adapter si page sous dossier/src/pages/exo/dom-events.html -->
     <script type="module">
        import { generateTableSkillsHTML } from '/src/services/skillsUiService.js';
        import { competencesData } from '/src/services/skillsDataService.js';
        document.addEventListener('DOMContentLoaded', () => {
            const tableContainer = document.getElementById('table-container');
            const generatedHTML = generateTableSkillsHTML(competencesData);
            tableContainer.innerHTML = generatedHTML;
        });
     </script>
    <script type="module" src="/src/main.js"></script>
</body>

</html>