<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Arrays JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <h1 class="text-3xl font-bold text-center mb-4">Exercice Tableaux</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE ARRAYS -----------------------------------
        //!---------------------------------------------------------------------------

        // Exercice 1
let prenom = 'JoSé';
let age = 45;
//! On déclare un tableau avec cette syntaxe : []
//! On peut placer ce que l'on veut dans un tableau 
let unTableau = [12,'Salut ca va bien?',prenom,age];
console.log(unTableau);
console.log(unTableau[2]);

//! Exemple avec un tableau dans un tableau 
let mesPassions = ["Boxe","Jonquilles"];
let monPerso = [prenom, age, mesPassions];

// Exercice 2
let testTabAjout = [10,10,10];
console.log('Avant Ajout :',testTabAjout);
testTabAjout.push('Cortex',92,'Les Pyramides');
console.log('Après Ajout : ',testTabAjout);

// Exercice 3 
let leNom = 'Garcia';
let lePrenom = 'José';
let initiales = lePrenom[0] + leNom[0];
let laPhrase = [];
laPhrase.push(leNom,lePrenom,initiales)
console.log(laPhrase);
    </script>
</body>

</html>