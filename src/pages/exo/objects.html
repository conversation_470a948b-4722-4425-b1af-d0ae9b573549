<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Objects JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <h1 class="text-3xl font-bold text-center mb-4">Exercice Objects</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE OBJECTS -----------------------------------
        //!---------------------------------------------------------------------------
// ! EXO 8 OBJECTS
// TODO : Refaire l'exo avec les passions en mode objet
let nameUser = 'Dong Rodrigue';
let ageUser = 65;

let objetUser = {
    'nom': nameUser,
    'age': ageUser,
    'passions': {
        passion1: 'Le Drift',
        passion2: 'Jonquilles'
    },

};

console.log('objet de utilisateur : ', objetUser);
console.log(objetUser.nom);
console.log(`Bienvenue ${objetUser.nom} Tu as maintenant ${objetUser.age} ans`)
console.log(objetUser['passions']);
console.log(objetUser.passions.passion2);
console.log(objetUser['passions']['passion2']);
    </script>
</body>

</html>