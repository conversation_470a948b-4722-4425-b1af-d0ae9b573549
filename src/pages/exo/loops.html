<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Boucles JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <div class="container mx-auto px-4 py-8">

                    <h1 class="text-4xl font-bold mb-6 text-center">Les systèmes de boucles</h1>

                    <div class="card bg-base-100 shadow-xl mb-8">
                        <!-- <figure class="px-10 pt-10">
                            <img src="https://picsum.photos/600/350" alt="Illustration des boucles"
                                class="rounded-xl" />
                        </figure> -->
                        <div class="card-body items-center text-center">
                            <!-- <div class="card-actions justify-end mb-4">
                                <a href="http://google.com" class="btn btn-primary">Clique sur le lien</a>
                            </div> -->

                            <div class="space-y-4 text-left">
                                <p class="text-base-content/80">1ER Lorem ipsum, dolor sit amet consectetur adipisicing
                                    elit. Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor,
                                    nihil asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis
                                    veniam impedit?</p>
                                <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing
                                    elit. Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor,
                                    nihil asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis
                                    veniam impedit?</p>
                                <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing
                                    elit. Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor,
                                    nihil asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis
                                    veniam impedit?</p>
                                <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing
                                    elit. Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor,
                                    nihil asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis
                                    veniam impedit?</p>
                                <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing
                                    elit. Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor,
                                    nihil asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis
                                    veniam impedit?</p>
                                <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing
                                    elit. Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor,
                                    nihil asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis
                                    veniam impedit?</p>
                            </div>

                        </div>
                    </div>

                    <!-- <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                        <div class="card bg-base-100 shadow-xl p-6">
                            <p class="text-base-content/80">Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                                Totam et rem accusantium dicta voluptatum quam suscipit, molestiae dolor, nihil
                                asperiores corrupti, quisquam ducimus! Sint, ipsa. Voluptates adipisci facilis veniam
                                impedit?</p>
                        </div>
                    </div> -->
                </div>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <!-- <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE BOUCLES -----------------------------------
        //!---------------------------------------------------------------------------
        // !------------- LES BOUCLES pour ARRAY MAP() ------------------
        // ! EXO MAP
        // TODO :JS map phase 1
        // TODO : côté template html rajouter plein de <p></p>
        // TODO :On va récupérer TOUS les <p> de notre page dans une variable lesTxt via getElementsByTagName
        // TODO :On va faire un console log de lesTxt 
        //* On récupère TOUS les <p>
        const lesTxt = document.body.getElementsByTagName("p");
        console.log(lesTxt);
        console.log(lesTxt[0].innerHTML);
        // lesTxt[0].innerHTML = "YOOOOOLOOOOOOO";
        // lesTxt[0].innerHTML = "YOOOOOLOOOOOOO";
        // const premierP = document.body.getElementsByTagName("p")[0];
        // const lesTxt = document.getElementsByTagName("p");
        // premierP.innerText = "YOOOOOLOOOOOOO";
        // console.log(lesTxt);
        // console.log(premierP);
        //HTMLCollection a un système d'indice aussi comme les tableau
        // console.log(lesTxt[0]);
        // lesTxt[0].innerText = 'SUPER';

        // let text = "ABCDEFG"
        // const myArr = Array.from(text);
        // console.log('-------------');
        // console.log(text);
        // console.log(myArr);

        //TODO JS map Phase 2 
        //TODO Avec la methode Array.from(), dans une nouvelle variable textesTab on va transformer notre htmlCollection en array
        //TODO On console log la variables textesTab 
        //* On transforme le HTMLCollection(qui contient tous nos <p>) en Array classique
        const textesTab = Array.from(lesTxt);
        console.log(textesTab);

        //TODO JS Map Phase 3 (on peut travailler sur un Array)
        //TODO Sur textesTab on va utiliser la ƒ° map(),
        //TODO dans map(), on va lui passer en param une fonction fléchée qui elle a en parametre une variable temporaire (auChoix)
        //TODO cette fonction fléchée elle va modifier le innerHTML ou innerText de la variable temporaire
        //* On utilise une variable temporaire uneCase pour que map stock chaque <P> dans
        //* Cette variable uneCase, on peut s'en servir pour modifier le innerHtml
        // textesTab.map(uneCase => uneCase.innerText = "<h1>LOL JE SUIS HACKERMAN</h1>" );
        // textesTab.map(uneCase => uneCase.innerHTML = "<h1>LOL JE SUIS HACKERMAN</h1>" );

        //? bonus syntaxe en ƒ° classic
        //! Dans la fonction que l'on passe dans map(), si on met un 2nd param
        //! C'est l'index du tableau
        textesTab.map(function (uneCaseTab, index) {
            uneCaseTab.innerText = "<h1>LOL JE SUIS HACKERMAN</h1>";
            uneCaseTab.style.color = "red";
            uneCaseTab.style.cursor = "pointer";
            console.log('System indice :', index)
            uneCaseTab.addEventListener('click', () => {
                console.log("SUPER CA CLICK");
            });
        });

        //? bonus syntaxe en ƒ° fléchée
        // textesTab.map((x,i) => {
        //     x.style.color = "red";
        //     console.log(i)
        // });
        // console.log(textesTab);

        // textesTab.map(toto => toto.addEventListener('click',megaClick));
        // textesTab.map(toto => toto.addEventListener('click',()=>{
        //     console.log("SUPER CA CLICK");
        // }));


        // function megaClick(){
        //     console.log('mega click ');
        // }

        //! JS map Phase 1

        // const lesTxt = document.getElementsByTagName('p');
        // console.log(lesTxt);

        // //! JS map Phase 2
        // let textesTab = Array.from(lesTxt);
        // console.log(textesTab);

        // //! JS map Phase 3
        // const map1 = textesTab.map(elementDontHTMLSeModifie => elementDontHTMLSeModifie.innerHTML="<h3>Un petit titre en plus<h3>" );
        // console.log(map1);




        // firstImg.addEventListener('click',()=>{
        //   console.log('CA CLICK A DONF');
        // });
    </script> -->
</body>

</html>