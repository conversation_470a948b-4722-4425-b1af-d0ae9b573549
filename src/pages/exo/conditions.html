<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Conditions JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <h1 class="text-3xl font-bold text-center mb-4">Exercice Conditions</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE CONDITIONS -----------------------------------
        //!---------------------------------------------------------------------------
//! EXO 7 - IF ELSE
// TODO: Créer une fonction reçoit un tableau de 3 notes et qui calcule une moyenne entre ces 3 notes
// TODO: Dans cette ƒ°, SI la moyenne est suppérieur ou égale à 15 on renvoi une string (très Bien) 
// TODO: Dans cette ƒ°, SINON SI la moyenne est suppérieur ou égale à 10 on renvoi une string (assez Bien) 
// TODO: Dans cette ƒ°, SINON renvoi une string (refus) 

function msgMentionBacOfficiel(tabNotes) {
    let moyenneCalc = (tabNotes[0]+tabNotes[1]+tabNotes[2])/tabNotes.length;
    console.log('la Moyenne au Bac : ',moyenneCalc);
    if (moyenneCalc>=16) {
        return "Tu as Gagné !"
    } else if (moyenneCalc >=10 && moyenneCalc<16) {
        return 'Assez Bien'
    } else {
        return 'YO T NUL GRO'
    }
};
console.log(msgMentionBacOfficiel([13,6,3]));


//! Exo 5.3 Lea moyenne au bac de Tibo
let notesTiboInShape = [0,3,4,6,18,19,4];
function calculerMoyenneAvecPhrase(notes) {
    let somme = 0;
    for (let i = 0; i < notes.length; i++) {
        somme += notes[i];
    }
    let moyenne = somme / notes.length;

    if (moyenne > 16) {
        return "Super";
    } else if (moyenne >= 10 && moyenne <= 16) {
        return "Presque";
    } else if (moyenne === 10) {
        return "C'est Juste";
    } else {
        return "C'est raté";
    }
}
let moyenneTiboInShape = calculerMoyenneAvecPhrase(notesTiboInShape);
console.log('La mention de de TiboInShape est :', moyenneTiboInShape);



    </script>
</body>

</html>