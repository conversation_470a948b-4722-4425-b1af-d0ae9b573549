<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Calculs JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                        <h1 class="text-3xl font-bold text-center mb-4">Exercice <PERSON>culs</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE Variables --------------------------------
        //!---------------------------------------------------------------------------
       //!-------INTRO JS LES CALCULS-------
//! Exo 2 Calculs : 
//TODO: Afficher différents résultats de calculs dans la console du navigateur

console.log(2**10);
console.log(20+33);
console.log(20-33);
console.log(20/33);
console.log(20*33);
// 🚨 Troll pour les décimales
console.log(2,33+10);
// Pour les décimales la virgule avec la notation en .
console.log(2.33+10);
console.log('LOL',10000);
console.log(0.1+0.2);
console.log(1+20*5);
console.log((1+20)*5);
console.log(10%2);
console.log(3%2);

// Incrémentation
let num = 0;
console.log(num+1);
//? Pour faire du Cumul : 
// num = num+1;
num +=1;//Notation raccourcie (assignation composé)
// num = num + 5;
num +=5;
// Notation raccourcie incrémentation de 1
console.log(num++);
console.log(num--);

    </script>
</body>

</html>