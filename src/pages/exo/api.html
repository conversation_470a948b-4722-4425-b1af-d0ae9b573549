<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>API JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <!-- Quizz à mettre Ici  -->
                <h1 class="text-3xl font-bold text-center mb-4">Exercice API</h1>
                <div id="pokeList" class="bg-base-200 p-4 rounded-box mb-4"></div>
                <div id="superPokemonList" class="flex flex-wrap justify-center gap-4"></div>


            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE API  -----------------------------------
        //!---------------------------------------------------------------------------
        import DOMPurify from "dompurify";

// de base une ƒ° => est anonyme, astuce pour désanonymiser, on la stocke dans une variable
const pokemonApiContact = async () => {
    const pokemonListe = document.getElementById('pokeList');
    console.log(pokemonListe);
    //Data va récup Toutes les données de l'api
    const pokemonData = await fetch('https://pokeapi.co/api/v2/pokemon');
    console.log('pokemonData',pokemonData);
    //Plutot que de Travailler sur la réponse, on va la transformé pour 
    //qu'elle deviennt un OBJET JS (+ pratique)
    const pokemonDataTransformed = await pokemonData.json();
    console.log('pokemonDataTransformed',pokemonDataTransformed);
    console.log(pokemonDataTransformed.results[0].name);
    // Boucle pour parcourir le tableau results dans la réponse
    pokemonDataTransformed.results.forEach(unPokemon => {
        let listElement = document.createElement('h3');
        listElement.innerText = unPokemon.name;
        pokemonListe.append(listElement);
    });
};
pokemonApiContact();

// const userDiv = document.getElementById('userApi');
// console.log(userDiv);
// const randomUserApi =async ()=>{
//     const userData = await fetch('https://randomuser.me/api/');
//     console.log(userData);
//     const userDataTransformed = await userData.json();
//     console.log(userDataTransformed);
//     console.log(userDataTransformed.results[0]);
//     let myUserData = userDataTransformed.results[0];
//     console.log(myUserData.name);

//     let userImg = document.createElement('img');
//     userImg.src = myUserData.picture.large;

//     let userName = document.createElement('h1');
//     userName.innerText = myUserData.name.first + ' ' + myUserData.name.last;

//     let userAdress = document.createElement('h2');
//     userAdress.innerText = `${myUserData.location.street.number} - ${myUserData.location.street.name} (${myUserData.location.city} - ${myUserData.location.country})`;

//     let userMail = document.createElement('h3');
//     userMail.innerText = myUserData.email;

//     let userPhone = document.createElement('h3');
//     userPhone.innerText = myUserData.phone;
    
//     userDiv.append(userImg,userName,userMail,userPhone,userAdress);
// }
// randomUserApi()


// const utilisateurDiv = document.getElementById('userApi');
// console.log(utilisateurDiv);
// const randomUserApi2 = () => {
//     fetch('https://randomuser.me/api/')
//         .then(userData => {
//             console.log(userData);
//             return userData.json();
//         })
//         .then(userDataTransformed => {
//             console.log(userDataTransformed);
//             console.log(userDataTransformed.results[0]);
//             let myUserData = userDataTransformed.results[0];
//             console.log(myUserData.name);

//             let userImg = document.createElement('img');

//             let userName = document.createElement('h1');
//             userName.innerText = myUserData.name.first + ' ' + myUserData.name.last;

//             let userAdress = document.createElement('h2');
//             userAdress.innerText = `${myUserData.location.street.number} - ${myUserData.location.street.name} (${myUserData.location.city} - ${myUserData.location.country})`;

//             let userMail = document.createElement('h3');
//             userMail.innerText = myUserData.email;

//             let userPhone = document.createElement('h3');
//             userPhone.innerText = myUserData.phone;
//             utilisateurDiv.append(userImg, userName, userMail, userPhone, userAdress);
//         })
//         .catch(error => console.error('Erreur:', error));
// }
// randomUserApi2();


let divPokemonUI = document.querySelector("#superPokemonList");

const fetchPokemonGen9 = async () => {
    try {
        const response = await fetch("https://tyradex.vercel.app/api/v1/gen/9");
        if (!response.ok) {
            throw new Error('Erreur lors de la récupération des données');
        }
        const data = await response.json();
        console.log(data);
        

        // Générer des cartes pour chaque Pokémon
        data.forEach(pokemon => {
            const cartePokemon = document.createElement('div');
            cartePokemon.classList.add('card', 'card-compact', 'bg-base-100', 'shadow-xl', 'w-72', 'p-3');
            // cartePokemon.style.width = '18rem'; // Cette ligne peut être supprimée si w-72 est suffisant
            cartePokemon.innerHTML = DOMPurify.sanitize(`
              <figure class="px-10 pt-10">
                <img src="${pokemon.sprites.regular}" alt="${pokemon.name.fr}" class="rounded-xl" />
              </figure>
              <div class="card-body items-center text-center">
                <h2 class="card-title">${pokemon.name.fr}</h2>
                <p>Type: ${pokemon.types.map(type => type.name).join(', ')}</p>
                <p>Poids: ${pokemon.weight}</p>
                <p>Taille: ${pokemon.height}</p>
            </div>`);
            divPokemonUI.appendChild(cartePokemon);
            // Ajouter la carte au conteneur
        });
    } catch (error) {
        console.error('Erreur:', error);
    }
}

fetchPokemonGen9();
    </script>
</body>

</html>