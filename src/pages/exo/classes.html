<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Classes JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <!-- Quizz à mettre Ici  -->
                <h1 class="text-3xl font-bold text-center mb-4">Exercice Classes</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE CLASSES  -----------------------------------
        //!---------------------------------------------------------------------------
///**
//  * **************************************
//  * Exo : IMC
//  * **************************************
//  */
class Imc {
    //* Constructor -> initialise les données
    constructor(nom, poids, taille) {
        this._nom = nom; //! 3 attributes en In mode
        this._poids = poids;
        this._taille = taille;
        this._imc = this.calculImc(); //! attribute en OUT mode (à calculer) c'est + easy pour affichage
    }
    //* Le calcul
    calculImc() {
        //* Format simple (2 nombres après le . ou la ,)
        // return this._poids/(this._taille*this._taille);
        //  return (this._poids/Math.pow(this._taille, 2)).toFixed(2);
        return (this._poids / this._taille ** 2).toFixed(2);
    }
    //* Affichage
    display() {
        console.log(`Bonjour,${this._nom} (${this._poids} kg, ${this._taille} M) a un IMC de: ${this._imc}`);
    }
}
//* progr principal -> on fait l'injection des données
let list = [
    new Imc("Sébastien Chabal", 135, 1.7),
    new Imc("Escaladeuse", 45, 1.68),
    new Imc("JOJO ", 300, 2),
    new Imc("Gontrand ", 90, 1.75),
    new Imc("Colonel Clock ", 200, 1.75),
    new Imc("JOsiane de la Vega", 99, 1.55),
    new Imc("LOLO de la Vega", 400, 1.55)
];
// //*Boucle forEach qui parcourt le tableau avec une variable temporaire uneCase
list.forEach((uneInstanceImc) => uneInstanceImc.display());

/**
 * **************************************
 * Exo : PME
 * **************************************
 */
console.log('------------------MA PME-----------------');
class Employee {
    constructor(nom, prenom, age, salaireMensuel) {
        this._nom = nom;
        this._prenom = prenom;
        this._age = age;
        this._salaireMensuel = salaireMensuel;
        this._cout = this.calculCout();// Calcul cout annuel de l'employé :attribut en outMode
    }
    // me servira à passer le cout d 1 employé dans la classe PME
    getCout() {
        return this._cout;
        // return this.calculCout();
    }
    //calcul cout total d 1 salarié
    calculCout() {
        const NB_MOIS_SAL = 12;
        const LA_TAXE = 0.9;
        //Un léger calcul
        return this._salaireMensuel * NB_MOIS_SAL * (1 + LA_TAXE);
    }
}

class Pme {
    // 
    constructor(nom, equipe, ventes, coutsFixes, achats) {
        this._nom = nom;
        this._equipe = equipe;
        this._cout = coutsFixes + achats;// On peut assigner directement un calcul ici
        this._ventes = ventes;
        this._bilan = 0;    // attribut en OutMode a calculer
    }

    bilanCalculed() {
        console.log(this._equipe);
        let cumulEquipe = 0;
        console.log(`${this._nom} : Cout Initial : ${this._cout}`);

        //Boucle qui parcourt le tableau des salariés, Employee (equipe)
        //Sur chaque salarié parcouru dans le tableau, on récupère et cumule le cout de ce Salarié
        for (let unSalarie of this._equipe) {
            cumulEquipe += unSalarie.getCout();
        }

        console.log(`${this._nom} : Cout Total Equipe : ${cumulEquipe}`);
        //Ensuite dans les couts de l'entreprise on cumul le cout de toute l'équipe
        this._cout += cumulEquipe;
        console.log(`${this._nom} : VENTES : ${this._ventes}`);
        //Dans les _cout on va avoir les frais fixe + frais achat et 
        //on vient de rajouter en + le cout total d'une equipe
        //donc le bilan de la pme : les ventes moins tous les couts (frais fixes, achat + cout total de l'equipe à l'année)
        this._bilan = this._ventes - this._cout;
        console.log(`${this._nom} : BILAN : ${this._bilan}`);
    }
}

// // Scénario
const pme = new Pme(
    //Le nom entreprise
    "Ma Petite Entreprise - ",
    //L'equipe de salariés (un tableau)
    [new Employee("Duval", "Paul", 30, 2000),
    new Employee("Durand", "Alain", 40, 3000),
    new Employee("Dois", "Sylvia", 50, 4000),],
    //le revenu , frais fixe, frais d'achat
    300000,
    20000,
    50000);
pme.bilanCalculed();

// /**
//  * **************************************
//  * Exo : BANK
//  * **************************************
//  */
console.log('------------------BANK-----------------');

class CompteBancaire {
    constructor(titulaire) {
        this.titulaire = titulaire;//IN MODE
        this.solde = 0;//OUT MODE
    }
    // Ajoute un montant au solde
    crediter(montant) {
        this.solde += montant;
        console.log("Ajout de: " + montant + " pour: " + this.titulaire);
    }
    // Retirer un montant au solde
    retirer(montant) {
        try {
            if (this.solde < montant)
                throw (
                    this.titulaire +
                    ", retrait de: " +
                    montant +
                    " refusé avec solde: " +
                    this.solde
                );
            this.solde -= montant;
            console.log("Retrait de: " + montant + " pour: " + this.titulaire);
        } catch (err) {
            console.log("Erreur Custom---------->" + err);
        }
    }
    virer(montant, membre) {
        console.log(
            "Virement de: " +
            montant +
            " de: " +
            this.titulaire +
            " vers: " +
            membre.titulaire
        );
        membre.crediter(montant);
        this.retirer(montant);
    }

    // Renvoie la description du compte
    decrire() {
        return "titulaire: " + this.titulaire + ", solde: " + this.solde;
    }
}

// Main, gère 3 comptes bancaires dans un tableau associatif
const lesComptes = {
    Alex: new CompteBancaire("Alex"),
    Clovis: new CompteBancaire("Clovis"),
    Marco: new CompteBancaire("Marco"),
};

// lecture tableau associatif ou Objet["truc"]
// Crédite et décrit chaque compte
for (let key in lesComptes) {
    lesComptes[key].crediter(1000);
}

// un retrait de 100 par Alex
lesComptes["Alex"].retirer(100);
// un petit virement: Marco Vire 300 à Clovis
lesComptes["Marco"].virer(300, lesComptes["Clovis"]);
// un petit retrait incorrect (doit déclencher erreur custom) : 
// Alex fait un retrait de 1200
lesComptes["Alex"].retirer(1200);
// bilan : faire une déscription de tous les comptes en console (ou DOM ?)
for (let key in lesComptes) {
    console.log(lesComptes[key].decrire());
}
console.log('------------------Bibliothèque-----------------');

class Livre {
    constructor(titre, auteur) {
        this.titre = titre;
        this.auteur = auteur;
        this.disponible = true;
    }

    emprunter() {
        try {
            if (!this.disponible) {
                throw new Error(`Le livre "${this.titre}" n'est pas disponible.`);
            }
            this.disponible = false;
        } catch (error) {
            console.error(error.message);
        }
    }

    retourner() {
        this.disponible = true;
    }
}

class Bibliotheque {
    constructor(nom) {
        this.nom = nom;
        this.livres = [];
    }

    ajouterLivre(livre) {
        this.livres.push(livre);
    }

    emprunterLivre(titre) {
        try {
            const livre = this.livres.find(unLivre => unLivre.titre === titre);
            // console.log(livre);
            // Si on a pas trouvé le livre, si le find nous a retourné undefined,
            // alors on lève une erreur custom
            if (!livre) {
                throw new Error(`Le livre "${titre}" n'existe pas dans la bibliothèque.`);
            }
            livre.emprunter();
        } catch (error) {
            console.error(error.message);
        }
    }

    retournerLivre(titre) {
        try {
            const livre = this.livres.find(unLivre => unLivre.titre === titre);
            if (!livre) {
                throw new Error(`Le livre "${titre}" n'a jamais été les stocks de la bibliothèque.`);
            }
            livre.retourner();
        } catch (error) {
            console.error(error.message);
        }
    }
}

// Exemple d'utilisation scénario
const maBibliotheque = new Bibliotheque("Ma Bibliothèque");
const livre1 = new Livre("1984", "George Orwell");
const livre2 = new Livre("Le Petit Prince", "Steven Seagal");
maBibliotheque.ajouterLivre(livre1);
maBibliotheque.ajouterLivre(livre2);
maBibliotheque.emprunterLivre("1984"); // Livre emprunté avec succès.
maBibliotheque.emprunterLivre("1984"); // Déclenche exception livre n'est plus disponible.
maBibliotheque.retournerLivre("1984"); // Livre retourné avec succès.
maBibliotheque.retournerLivre("Bratisla Bios"); // Le Livre n'a jamais existé dans la bibliothèque.
maBibliotheque.emprunterLivre("198999"); // Déclenche exception livre n'existe pas.

console.log('------------------Magasin-----------------');

class Produit {
    constructor(nom, prix, quantite) {
        this.nom = nom;
        this.prix = prix;
        this.quantite = quantite;
    }

    disponible() {
        return this.quantite > 0;
    }
}

class Magasin {
    constructor() {
        this.produits = [];
    }

    ajouterProduit(produit) {
        const existe = this.produits.some(p => p.nom === produit.nom);
        if (existe) {
            throw new Error(`Le produit "${produit.nom}" existe déjà dans le magasin.`);
        }
        this.produits.push(produit);
    }

    chercherProduit(nom) {
        const produit = this.produits.find(p => p.nom === nom);
        if (!produit) {
            throw new Error(`Le produit "${nom}" n'a pas été trouvé.`);
        }
        return produit;
    }
}

// Code principal
const magasin = new Magasin();

try {
    const produit1 = new Produit("Pomme", 1.5, 10);
    magasin.ajouterProduit(produit1);
    
    const produit2 = new Produit("Banane", 1.2, 0);
    magasin.ajouterProduit(produit2);
    
    // Essayer d'ajouter un produit existant
    const produit3 = new Produit("Pomme", 1.5, 5);
    magasin.ajouterProduit(produit3); // Cela devrait lancer une exception

} catch (error) {
    console.log("Erreur : " + error.message);
}

try {
    const recherche = magasin.chercherProduit("Orange"); // Cela devrait lancer une exception
} catch (error) {
    console.log("Erreur : " + error.message);
}

console.log("------------------Billets Concerts--------------");
class Billet {
    constructor(type, prix) {
        this.type = type;
        this.prix = prix;
        this.disponible = true;
    }

    reserver() {
        if (!this.disponible) {
            throw new Error(`Le billet "${this.type}" n'est pas disponible.`);
        }
        this.disponible = false;
    }

    annuler() {
        this.disponible = true;
    }
}

class Utilisateur {
    constructor(nom, role) {
        this.nom = nom;
        this.role = role;
    }

    reserverBillet(billet) {
        billet.reserver();
    }

    annulerBillet(billet) {
        billet.annuler();
    }
}

class Client extends Utilisateur {
    constructor(nom, email) {
        super(nom, "client");
        this.email = email;
    }

    reserverBillet(billet) {
        if (!billet.disponible) {
            throw new Error(`Le billet "${billet.type}" n'est pas disponible pour ${this.nom}.`);
        }
        super.reserverBillet(billet);
    }
}

class Administrateur extends Utilisateur {
    constructor(nom) {
        super(nom, "administrateur");
    }

    ajouterBillet(billet, evenement) {
        evenement.ajouterBillet(billet);
    }
}

class Evenement {
    constructor(nom, date) {
        this.nom = nom;
        this.date = date;
        this.billets = [];
    }

    ajouterBillet(billet) {
        const existe = this.billets.some(b => b.type === billet.type);
        if (existe) {
            throw new Error(`Le billet "${billet.type}" existe déjà pour l'événement.`);
        }
        this.billets.push(billet);
    }

    chercherBillet(type) {
        const billet = this.billets.find(b => b.type === type);
        if (!billet) {
            throw new Error(`Le billet "${type}" n'existe pas pour l'événement.`);
        }
        return billet;
    }
}

// Exemple d'utilisation
const evenement = new Evenement("Concert de Rock", "2023-12-01");
const billetStandard = new Billet("standard", 50);
const billetVIP = new Billet("VIP", 100);

const administrateur = new Administrateur("Alice");
administrateur.ajouterBillet(billetStandard, evenement);
administrateur.ajouterBillet(billetVIP, evenement);

const client = new Client("Bob", "<EMAIL>");
try {
    client.reserverBillet(billetStandard); // Billet réservé avec succès.
    client.reserverBillet(billetStandard); // Déclenche exception billet n'est plus disponible.
} catch (error) {
    console.log(error.message);
}

    </script>
</body>

</html>