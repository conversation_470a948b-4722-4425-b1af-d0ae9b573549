<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Strings JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <h1 class="text-3xl font-bold text-center mb-4">Exercice Strings</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE Strings --------------------------------
        //!---------------------------------------------------------------------------
        let userName = 'Toto';
        let pizzaName = 'Calzone';
        let date = "01-01-2024";
        let adress = '11 avenue de l\'europe';


        let sumUpOrderPhrase = 'Le ' + date + ' Bonjour Mr ' + userName + ' votre pizza : ' + pizzaName + ' est en cours de préparations' + ' elle sera livrée dans 30 minutes à :' + adress;
        console.log(sumUpOrderPhrase);
        let sumUpPhrase = `Le ${date} : 
Bonjour Mr ${userName}, votre pizza : ${pizzaName} est en cours de préparation.
Elle vous sera livrée d'ici 30minutes au ${adress}
Merci d'avoir commandé chez la pizerria "Rafinata"`

        console.log(sumUpPhrase);




    </script>
</body>

</html>