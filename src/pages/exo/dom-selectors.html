<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>DOM Selectors JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div id="navbar-container"></div>
        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <!-- Le contenu spécifique de la leçon sera inséré ici -->
          <h1 id="main-title" class="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">Exercices sur Les DOM Selectors en JavaScript</h1>
          <p class="py-4 sm:py-6 text-sm sm:text-base">Exercices pratiques sur les sélecteurs DOM en JavaScript.</p>
          <h2>On teste d'espionner le clavier</h2>
          <input type="text" id="input-text" placeholder="Entrez votre texte" class="input input-bordered w-full max-w-xs">
          <div id="renderKey"></div>
          <button id="btn-clic-me" class="btn btn-soft btn-primary mt-4">Primary</button>
        </main>
        <!-- Footer -->
        <div id="footer-container"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module" src="/src/pages/exo/exo.js"></script>
  </body>
</html>
