<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Scope Functions JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <!-- Quizz à mettre Ici  -->
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE SCOPE  -----------------------------------
        //!---------------------------------------------------------------------------
        // !------------- LES BOUCLES pour ARRAY MAP() ------------------
//TODO : Pourquoi ca beug ?
function buggyFunction() {
    let wtf = 9;
    console.log(wtf);
}; 
buggyFunction();
// console.log(wtf);

// //TODO : Pourquoi ca beug II / Pourquoi ca marche pas ?
// let something = 44;
// function functionBugParent() {
//     let something = 9;
//     console.log(something);
    
    
//     function functionBugEnfant() {
//         let lesNews = `il est 99h67`;
//         console.log(lesNews);
//     }
// };
// functionBugParent();
// console.log(something);

function choixVoiture(){
    var uneVoiture = "Harley Davidson"
}

choixVoiture();
console.log(uneVoiture);

let car = "Nissan";

if(car=="Nissan"){
    const vitesse = 800;
}
console.log(vitesse);

    </script>
</body>

</html>