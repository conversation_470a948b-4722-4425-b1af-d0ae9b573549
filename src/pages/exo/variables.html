<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Variables JavaScript - Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>
            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                        <h1 class="text-3xl font-bold text-center mb-4">Exercice Variables</h1>
            </main>
            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- EXERCICE Variables --------------------------------
        //!---------------------------------------------------------------------------
        //! EXO 1 VARIABLES         //TODO: On doit se débrouiller pour créer une variable, 
        //TODO: Lui Assigner une valeur (genre un nombre ou une chaine de caractère)
        //TODO: On doit afficher cette Variable dans la console du navigateur
        //Déclaration
        let monNumFetiche;
        // Assignation
        monNumFetiche = 77;
        // Déclaration + Assignation
        let maVariable = 11;
        let uneAutreVariable = 'Steven Seagal';
        let monTabloClient = ['Client1', maVariable, 109];
        let uneVariableObjet = {
            // ↓ Propriétés de l'objet (auxquelles on assigne 
            //des valeur mais avec les : pas le =)
            'ChuckNorris': 99,
            'StevenSIgal': 100,
            'BruceWillis': 'DieHard',
            unePropriete: 'lol',
        };

        let maFonctionHello = function () {
            console.log('Coucou');
        }
        console.log(maVariable);
        console.log('-------TYPE OF -------');
        console.log(typeof maVariable);
        console.log(typeof 123);
        console.log(typeof "yoyoyo");
        console.log(uneAutreVariable);
        console.log(monTabloClient);
        console.log(monTabloClient[1]);
        console.log(uneVariableObjet);
        console.log(uneVariableObjet.BruceWillis);//Notation en point
        console.log(uneVariableObjet.unePropriete);//Notation en point
        console.log(uneVariableObjet['ChuckNorris']);//tableau associatif
        console.log(uneVariableObjet['unePropriete']);//tableau associatif
        console.log('HELLO WORLD');
        maFonctionHello();
        console.log(maFonctionHello);

    </script>
</body>

</html>