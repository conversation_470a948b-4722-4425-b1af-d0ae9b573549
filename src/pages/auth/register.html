<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <!-- <link rel="icon" type="image/svg+xml" href="/vite.svg" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 flex items-center justify-center px-6 py-12">
                <div class="w-full max-w-md">
                    <!-- Card de connexion -->
                    <div class="card bg-base-100 shadow-2xl border border-base-300">
                        <div class="card-body">
                            <!-- Header -->
                            <div class="text-center mb-6">
                                <div class="avatar placeholder mb-4">
                                    <div class="bg-primary text-primary-content rounded-full w-16">
                                        <span class="text-2xl">🔐</span>
                                    </div>
                                </div>
                                <h1 class="text-3xl font-bold text-base-content">Connexion</h1>
                                <p class="text-base-content/70 mt-2">Connectez-vous à votre compte</p>
                            </div>

                            <!-- Formulaire -->
                            <form class="space-y-4" id="loginForm">
                                <!-- Email -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Email</span>
                                    </label>
                                    <div class="relative">
                                        <input
                                            type="email"
                                            placeholder="<EMAIL>"
                                            class="input input-bordered w-full pl-10 focus:input-primary"
                                            id="email"
                                            required
                                        />
                                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Mot de passe -->
                                <div class="form-control">
                                    <label class="label">
                                        <span class="label-text font-medium">Mot de passe</span>
                                    </label>
                                    <div class="relative">
                                        <input
                                            type="password"
                                            placeholder="••••••••"
                                            class="input input-bordered w-full pl-10 pr-10 focus:input-primary"
                                            id="password"
                                            required
                                        />
                                        
                                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                        <button
                                            type="button"
                                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
                                            id="togglePassword"
                                        >
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="eyeIcon">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </button>
                                        
                                    </div>
                                    <!-- Message d'erreur du mot de passe -->
                                    <div id="passwordError"></div>
                                </div>

                                <!-- Options -->
                                <div class="flex items-center justify-between">
                                    <label class="label cursor-pointer">
                                        <input type="checkbox" class="checkbox checkbox-primary checkbox-sm" id="rememberMe" />
                                        <span class="label-text ml-2">Se souvenir de moi</span>
                                    </label>
                                    <a href="#" class="link link-primary text-sm">Mot de passe oublié ?</a>
                                </div>

                                <!-- Bouton de connexion -->
                                <div class="form-control mt-6">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <span class="loading loading-spinner loading-sm hidden" id="loginSpinner"></span>
                                        <span id="loginText">Se connecter</span>
                                    </button>
                                </div>
                            </form>

                            <!-- Divider -->
                            <!-- <div class="divider">ou</div> -->

                            <!-- Connexion sociale -->
                            <!-- <div class="space-y-3">
                                <button class="btn btn-outline btn-block">
                                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                        <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                        <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                        <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                        <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                    </svg>
                                    Continuer avec Google
                                </button>

                                <button class="btn btn-outline btn-block">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                    Continuer avec Facebook
                                </button>
                            </div> -->

                            <!-- Lien d'inscription -->
                            <div class="text-center mt-6">
                                <p class="text-base-content/70">
                                    Pas encore de compte ?
                                    <a href="#" class="link link-primary font-medium">Créer un compte</a>
                                </p>
                            </div>
                        </div>
                    </div>


                </div>
            </main>
                

            <!-- Footer -->
            <div id="footer-container"></div>

        </div>
    </div>
    <script type="module" src="/src/main.js"></script>

    <!-- <script type="module">
        import {
            setupRealTimeValidation,
            validateForm,
            showAlert,
            clearAllAlerts,
            FormConfigs
        } from '/src/services/formValidationService.js';

        // Configuration du formulaire de connexion
        const loginFormConfig = FormConfigs.login;

        // Initialisation de la validation en temps réel
        document.addEventListener('DOMContentLoaded', function() {
            // Configuration de la validation en temps réel
            setupRealTimeValidation(loginFormConfig, {
                validateOnInput: true,
                validateOnBlur: true,
                showSuccess: true,
                debounceTime: 500
            });

            // Animation d'entrée de la card
            const card = document.querySelector('.card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);

            // Effets de focus sur les inputs
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('scale-105');
                    this.parentElement.style.transition = 'transform 0.2s ease';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('scale-105');
                });
            });
        });

        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                `;
            }
        });

        // Form submission avec validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Nettoyer les alertes précédentes
            clearAllAlerts();

            // Valider le formulaire
            const validationResult = validateForm(loginFormConfig);

            if (!validationResult.isValid) {
                showAlert('error', 'Veuillez corriger les erreurs dans le formulaire');
                return;
            }

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // Show loading state
            const spinner = document.getElementById('loginSpinner');
            const loginText = document.getElementById('loginText');
            const submitBtn = e.target.querySelector('button[type="submit"]');

            spinner.classList.remove('hidden');
            loginText.textContent = 'Connexion...';
            submitBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Reset button state
                spinner.classList.add('hidden');
                loginText.textContent = 'Se connecter';
                submitBtn.disabled = false;

                // Simple validation for demo
                if (email === '<EMAIL>' && password === 'password') {
                    showAlert('success', 'Connexion réussie ! Redirection en cours...', {
                        autoHide: false
                    });

                    // Simulate redirect after success
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    showAlert('error', 'Email ou mot de passe incorrect');
                }
            }, 2000);
        });
    </script> -->

    <!-- Script pour utiliser la fonction améliorée de validation de mot de passe -->
    <script type="module">
        import { passwordValidateInput } from '/src/services/formValidationService.js';

        // Initialisation de la validation du mot de passe avec options personnalisées
        document.addEventListener('DOMContentLoaded', function() {
            passwordValidateInput('password', 'passwordError', {
                minLength: 6,
                maxLength: 20,
                requireDigit: true,
                requireSpecialChar: true,
                requireUppercase: false,  // Pas obligatoire pour la connexion
                requireLowercase: false,  // Pas obligatoire pour la connexion
                specialChars: '$&@!',
                showStrengthIndicator: true,
                debounceTime: 300
            });

            // Toggle password visibility (fonction existante)
            const togglePassword = document.getElementById('togglePassword');
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const passwordInput = document.getElementById('password');
                    const eyeIcon = document.getElementById('eyeIcon');

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        eyeIcon.innerHTML = `
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                        `;
                    } else {
                        passwordInput.type = 'password';
                        eyeIcon.innerHTML = `
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        `;
                    }
                });
            }

            // Animation d'entrée de la card
            const card = document.querySelector('.card');
            if (card) {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease-out';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }
        });
    </script>
</body>

</html>