<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Setup d’un projet JS avec Vite | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">

            <!-- Navbar -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">

                <!-- Hero Section -->
                <section class="text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">Setup d’un projet JavaScript avec Vite</h1>
                    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
                        Découvrez comment configurer rapidement un projet JavaScript moderne en utilisant
                        <span class="font-bold">Vite</span>, un outil de build ultra-rapide et simple à utiliser.
                    </p>
                </section>

                <!-- Qu'est-ce que Vite ? -->
                <section class="mb-12">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">⚡ Qu’est-ce que Vite ?</h2>
                            <p class="mt-3">
                                <span class="font-bold">Vite</span> est un bundler moderne qui permet de développer des
                                applications JavaScript
                                ou TypeScript avec une rapidité exceptionnelle grâce à son serveur de développement basé
                                sur
                                <code>ESM (ECMAScript Modules)</code>.
                            </p>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>Démarrage instantané du serveur de dev.</li>
                                <li>Hot Module Replacement (HMR) ultra-rapide.</li>
                                <li>Compatible avec <span class="badge badge-primary">Vue</span>, <span
                                        class="badge badge-secondary">React</span>, <span
                                        class="badge badge-accent">Svelte</span>, etc.</li>
                                <li>Support natif de TypeScript et PostCSS.</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Installation -->
                <section class="mb-12">
                    <div class="card bg-primary text-primary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">🛠️ Installation de Vite</h2>
                            <p class="mb-4">
                                Assurez-vous d’avoir installé <code>Node.js</code> (version 18 minimum recommandée).
                            </p>
                            <pre class="mockup-code text-left">
                <code># Créer un projet avec npm
npm create vite@latest

# ou avec pnpm
pnpm create vite

# ou avec yarn
yarn create vite</code>
              </pre>
                            <p class="mt-4">Choisissez ensuite :</p>
                            <ul class="list-disc list-inside mt-2">
                                <li>Nom du projet (ex: <code>mon-projet</code>)</li>
                                <li>Framework (ex: Vanilla, Vue, React...)</li>
                                <li>Langage (JavaScript ou TypeScript)</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Structure du projet -->
                <section class="mb-12">
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">📂 Structure d’un projet Vite</h2>
                            <p class="mb-4">
                                Voici la structure de base générée par Vite :
                            </p>
                            <pre class="mockup-code text-left">
                <code>mon-projet/
├── index.html
├── package.json
├── vite.config.js
├── /node_modules
└── /src
    ├── main.js
    └── style.css</code>
              </pre>
                            <p class="mt-4">
                                Le fichier <code>main.js</code> est le point d’entrée de l’application.
                                Vous pouvez y importer votre CSS et vos modules JS.
                            </p>
                        </div>
                    </div>
                </section>

                <!-- Lancer le projet -->
                <section class="mb-12">
                    <div class="card bg-accent text-accent-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">🚀 Lancer le projet</h2>
                            <p class="mb-4">Utilisez les commandes suivantes :</p>
                            <pre class="mockup-code text-left">
                <code># Installer les dépendances
npm install

# Lancer le serveur de dev
npm run dev

# Build du projet
npm run build</code>
              </pre>
                            <p class="mt-4">
                                Par défaut, le serveur de développement est accessible à
                                <a href="http://localhost:5173" target="_blank"
                                    class="link link-hover font-bold">http://localhost:5173</a>
                            </p>
                        </div>
                    </div>
                </section>

                <!-- Bonnes pratiques -->
                <section>
                    <div class="card bg-neutral text-neutral-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">✅ Bonnes pratiques</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>Versionner <code>package.json</code> et <code>vite.config.js</code>.</li>
                                <li>Ne pas versionner <code>node_modules/</code> (utiliser un <code>.gitignore</code>).
                                </li>
                                <li>Utiliser <code>.env</code> pour stocker les configurations sensibles.</li>
                                <li>Organiser son code dans des sous-dossiers <code>/components</code>,
                                    <code>/utils</code>, etc.</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>

            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>

</html>