<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Les Web Workers | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">

            <div id="navbar-container"></div>

            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">

                <section class="text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">Les Web Workers</h1>
                    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
                        Les Web Workers permettent d'exécuter des scripts en arrière-plan dans un thread séparé du
                        thread principal de l'interface utilisateur. Cela vous permet d'effectuer des tâches lourdes
                        sans bloquer l'interface.
                    </p>
                </section>

                <section class="mb-12">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">👉 Pourquoi utiliser les Web Workers ?</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>**Éviter les blocages** : Le JavaScript dans les navigateurs est par défaut
                                    "mono-thread". Une tâche complexe peut bloquer l'interface et la rendre non
                                    réactive.</li>
                                <li>**Multitâche** : Les Web Workers permettent d'exécuter des calculs lourds, des
                                    requêtes API complexes ou des opérations de traitement de données en parallèle.</li>
                                <li>**Améliorer l'expérience utilisateur** : L'interface reste fluide et réactive
                                    pendant que le travail se fait en arrière-plan.</li>
                            </ul>
                            <p class="mt-4">
                                La communication entre le thread principal et le Web Worker se fait par l'envoi de
                                messages.
                            </p>
                        </div>
                    </div>
                </section>

                <section class="mb-12">
                    <div class="card bg-primary text-primary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">⚡ Exemple : Lancer une tâche lourde</h2>
                            <p class="mb-4">
                                Imaginez un calcul qui prend beaucoup de temps. Sans Web Worker, votre page gèlerait.
                                Avec, elle reste fluide.
                            </p>
                            <div class="space-y-6">
                                <div>
                                    <h3 class="font-bold text-lg mb-2">Fichier principal (main.js)</h3>
                                    <pre class="mockup-code text-left">
                    <code>
const monWorker = new Worker('worker.js');

// Envoyer un message au Worker
monWorker.postMessage('Commencer le calcul');

// Réceptionner un message du Worker
monWorker.onmessage = function(e) {
  console.log('Résultat du calcul :', e.data);
};

// Gérer les erreurs du Worker
monWorker.onerror = function(e) {
  console.error('Erreur du Worker:', e.message);
};
                    </code>
                  </pre>
                                </div>

                                <div>
                                    <h3 class="font-bold text-lg mb-2">Fichier du Worker (worker.js)</h3>
                                    <p>
                                        Le Worker a son propre scope et ne peut pas accéder au DOM.
                                    </p>
                                    <pre class="mockup-code text-left">
                    <code>
// Réceptionner un message du thread principal
onmessage = function(e) {
  if (e.data === 'Commencer le calcul') {
    // Tâche très lourde
    let somme = 0;
    for (let i = 0; i < 1000000000; i++) {
      somme += i;
    }

    // Envoyer le résultat au thread principal
    postMessage(somme);
  }
};
                    </code>
                  </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section>
                    <div class="card bg-neutral text-neutral-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">✅ Bonnes pratiques et limitations</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>Les Workers ne peuvent pas accéder au **DOM** (l'objet `document`). Toute
                                    manipulation d'interface doit être faite dans le thread principal après réception du
                                    résultat.</li>
                                <li>La communication entre les threads se fait par copie de données. Évitez de
                                    transférer de très grandes quantités de données si ce n'est pas nécessaire.</li>
                                <li>Utilisez les Workers pour des tâches qui durent plus de 50 ms et qui ne nécessitent
                                    pas de mise à jour de l'interface pendant l'exécution.</li>
                                <li>Pensez à fermer le Worker (`monWorker.terminate()`) une fois qu'il a terminé son
                                    travail pour libérer des ressources.</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>

            <div id="footer-container"></div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>

</html>