<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Leçon : JavaScript Modulaire | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">

            <!-- Navbar -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">

                <!-- Hero -->
                <section class="text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">📦 JavaScript Modulaire</h1>
                    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
                        Découvrez comment organiser votre code avec les <strong>modules JavaScript</strong>,
                        en utilisant <code>import</code> et <code>export</code>.
                        Cette approche vous aide à structurer vos projets comme dans les frameworks modernes.
                    </p>
                </section>

                <!-- Pourquoi les modules -->
                <section class="mb-10">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">📌 Pourquoi utiliser des modules ?</h2>
                            <p class="mt-2">
                                Le JavaScript modulaire permet de :
                            </p>
                            <ul class="list-disc list-inside mt-3 space-y-2">
                                <li>Découper le code en plusieurs fichiers plus lisibles</li>
                                <li>Réutiliser facilement des fonctions ou classes</li>
                                <li>Éviter les variables globales qui polluent le scope</li>
                                <li>Se rapprocher de l’organisation qu’on retrouve dans des frameworks (Vue, React,
                                    Angular)</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Exemple export -->
                <section class="mb-10">
                    <div class="card bg-primary text-primary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">⚡ Exporter des fonctions ou variables</h2>
                            <p>
                                On peut définir des fonctions dans un fichier et les rendre disponibles ailleurs avec
                                <code>export</code>.
                            </p>
                            <pre class="mockup-code text-left mt-4">
<code>// utils/math.js
export function add(a, b) {
  return a + b;
}

export const PI = 3.14159;</code>
              </pre>
                        </div>
                    </div>
                </section>

                <!-- Exemple import -->
                <section class="mb-10">
                    <div class="card bg-secondary text-secondary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">📥 Importer des modules</h2>
                            <p>
                                Dans un autre fichier, on peut importer ce qui a été exporté :
                            </p>
                            <pre class="mockup-code text-left mt-4">
<code>// main.js
import { add, PI } from "./utils/math.js";

console.log(add(2, 3)); // 5
console.log(PI);        // 3.14159</code>
              </pre>
                            <p class="mt-4">✅ Vite et les navigateurs modernes supportent nativement
                                <code>type="module"</code>.</p>
                        </div>
                    </div>
                </section>

                <!-- Export default -->
                <section class="mb-10">
                    <div class="card bg-accent text-accent-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">🌟 Export par défaut</h2>
                            <p>
                                Un fichier peut aussi avoir un <code>export default</code>, utile pour exporter une
                                seule fonctionnalité principale.
                            </p>
                            <pre class="mockup-code text-left mt-4">
<code>// services/userService.js
export default class UserService {
  constructor() {
    this.users = [];
  }
  addUser(user) {
    this.users.push(user);
  }
}</code>
              </pre>
                            <pre class="mockup-code text-left mt-4">
<code>// main.js
import UserService from "./services/userService.js";

const service = new UserService();
service.addUser({ name: "Alice" });</code>
              </pre>
                        </div>
                    </div>
                </section>

                <!-- Organisation -->
                <section class="mb-10">
                    <div class="card bg-neutral text-neutral-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">📂 Organisation des fichiers</h2>
                            <p class="mb-3">
                                Exemple d’arborescence d’un projet avec modules :
                            </p>
                            <pre class="mockup-code text-left">
<code>src/
 ├── main.js
 ├── utils/
 │    └── math.js
 ├── services/
 │    └── userService.js
 └── components/
      └── navbar.js</code>
              </pre>
                            <p class="mt-3">
                                👉 Cette organisation se rapproche de ce qu’on retrouve dans des frameworks comme Vue ou
                                React.
                            </p>
                        </div>
                    </div>
                </section>

            </main>

            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>

</html>