import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { lessonsData } from '/src/services/lessonsDataService.js';
// Code existant pour les événements clavier (conservé pour compatibilité)
document.addEventListener('DOMContentLoaded', () => {
    // Lessons Event Clavier (code existant)
    const inputTextElement = document.getElementById('input-text');
    const renderKeyElement = document.getElementById('renderKey');

    if (inputTextElement && renderKeyElement) {
        inputTextElement.addEventListener('keyup', (eventDuclavier) => {
            console.log('eventDuclavier.key', eventDuclavier.key);
            renderKeyElement.innerHTML = DOMPurify.sanitize(marked.parse(inputTextElement.value));
        });
    }

    // Variables d'environnement (code existant)
    console.log(import.meta.env.VITE_SOME_KEY) // "123"
    console.log(import.meta.env.DB_PASSWORD) // undefined
});

/**
 * Génère une carte leçon avec DaisyUI
 * @param {Object} lesson - Les données de la leçon
 * @returns {HTMLElement} L'élément carte généré
 */
function createLessonCard(lesson) {
    const card = document.createElement('div');
    card.className = 'card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-300';

    // Couleurs selon la difficulté
    const difficultyColors = {
        'Débutant': 'badge-success',
        'Intermédiaire': 'badge-warning',
        'Avancé': 'badge-error'
    };

    // Couleurs selon la catégorie
    const categoryColors = {
        'Fondamentaux': 'badge-primary',
        'DOM': 'badge-secondary',
        'Configuration': 'badge-accent',
        'Outils': 'badge-info'
    };

    // Couleurs selon le statut
    const statusConfig = {
        'available': {
            buttonClass: 'btn-primary',
            buttonText: 'Commencer la leçon',
            disabled: false
        },
        'coming-soon': {
            buttonClass: 'btn-disabled',
            buttonText: 'Bientôt disponible',
            disabled: true
        },
        'completed': {
            buttonClass: 'btn-success',
            buttonText: 'Revoir la leçon',
            disabled: false
        }
    };

    const config = statusConfig[lesson.status] || statusConfig['available'];

    card.innerHTML = `
        <div class="card-body">
            <div class="flex justify-between items-start mb-2">
                <div class="flex items-center gap-2">
                    <span class="text-sm font-mono text-base-content/60">#${lesson.order}</span>
                    <h2 class="card-title text-lg font-bold">${lesson.title}</h2>
                </div>
                <div class="flex gap-1">
                    ${lesson.badge ? `<div class="badge badge-secondary badge-sm">${lesson.badge}</div>` : ''}
                    <div class="badge ${categoryColors[lesson.category]} badge-sm">${lesson.category}</div>
                </div>
            </div>

            <p class="text-sm text-base-content/70 mb-4">${lesson.description}</p>

            <div class="flex flex-wrap gap-2 mb-4">
                ${lesson.topics.map(topic => `<span class="badge badge-outline badge-sm">${topic}</span>`).join('')}
            </div>

            <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${lesson.duration}</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="badge ${difficultyColors[lesson.difficulty]} badge-sm">${lesson.difficulty}</span>
                </div>
            </div>

            <div class="card-actions justify-end">
                <button class="btn ${config.buttonClass} btn-sm"
                        ${config.disabled ? 'disabled' : ''}
                        onclick="openLesson('${lesson.file}', '${lesson.id}')">
                    ${config.buttonText}
                </button>
            </div>
        </div>
    `;

    return card;
}

/**
 * Fonction pour ouvrir une leçon
 * @param {string} filename - Le nom du fichier leçon
 * @param {string} lessonId - L'ID de la leçon
 */
function openLesson(filename, lessonId) {
    console.log(`Ouverture de la leçon: ${lessonId} (${filename})`);

    // Rediriger vers la leçon
    window.location.href = `/src/pages/lessons/${filename}`;

    // Optionnel: Sauvegarder le progrès dans localStorage
    const progress = JSON.parse(localStorage.getItem('lessons-progress') || '{}');
    progress[lessonId] = {
        lastAccessed: new Date().toISOString(),
        status: 'started'
    };
    localStorage.setItem('lessons-progress', JSON.stringify(progress));
}

/**
 * Filtre les leçons selon différents critères
 * @param {string} filterType - Type de filtre ('all', 'difficulty', 'category', 'status')
 * @param {string} filterValue - Valeur du filtre
 */
function filterLessons(filterType, filterValue) {
    let filteredData = lessonsData;

    if (filterType === 'difficulty' && filterValue !== 'all') {
        filteredData = lessonsData.filter(lesson => lesson.difficulty === filterValue);
    } else if (filterType === 'category' && filterValue !== 'all') {
        filteredData = lessonsData.filter(lesson => lesson.category === filterValue);
    } else if (filterType === 'status' && filterValue !== 'all') {
        filteredData = lessonsData.filter(lesson => lesson.status === filterValue);
    }

    renderLessonsList(filteredData);
}

/**
 * Rend la liste des leçons dans le conteneur
 * @param {Array} data - Les données des leçons à afficher
 */
function renderLessonsList(data = lessonsData) {
    const container = document.getElementById('lessons-container');
    if (!container) {
        console.warn('Conteneur leçons non trouvé');
        return;
    }

    // Vider le conteneur
    container.innerHTML = '';

    // Trier par ordre
    const sortedData = [...data].sort((a, b) => a.order - b.order);

    // Ajouter les cartes
    sortedData.forEach(lesson => {
        const card = createLessonCard(lesson);
        container.appendChild(card);
    });

    // Afficher un message si aucune leçon trouvée
    if (data.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <div class="text-6xl mb-4">📖</div>
                <h3 class="text-xl font-semibold mb-2">Aucune leçon trouvée</h3>
                <p class="text-base-content/60">Essayez de modifier vos filtres ou revenez plus tard.</p>
            </div>
        `;
    }
}

/**
 * Crée la barre de filtres pour les leçons
 * @returns {HTMLElement} L'élément de filtres
 */
function createLessonsFilterBar() {
    const filterBar = document.createElement('div');
    filterBar.className = 'bg-base-200 rounded-lg p-4 mb-6';

    // Obtenir les catégories uniques
    const categories = [...new Set(lessonsData.map(lesson => lesson.category))];

    filterBar.innerHTML = `
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex items-center gap-2">
                <span class="text-sm font-medium">Filtrer par:</span>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Catégorie</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterLessons('category', this.value)">
                    <option value="all">Toutes</option>
                    ${categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
                </select>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Difficulté</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterLessons('difficulty', this.value)">
                    <option value="all">Toutes</option>
                    <option value="Débutant">Débutant</option>
                    <option value="Intermédiaire">Intermédiaire</option>
                    <option value="Avancé">Avancé</option>
                </select>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Statut</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterLessons('status', this.value)">
                    <option value="all">Tous</option>
                    <option value="available">Disponible</option>
                    <option value="coming-soon">Bientôt</option>
                    <option value="completed">Terminé</option>
                </select>
            </div>

            <div class="ml-auto">
                <div class="stats stats-horizontal shadow-sm">
                    <div class="stat py-2 px-4">
                        <div class="stat-title text-xs">Total Leçons</div>
                        <div class="stat-value text-lg">${lessonsData.length}</div>
                    </div>
                    <div class="stat py-2 px-4">
                        <div class="stat-title text-xs">Catégories</div>
                        <div class="stat-value text-lg">${categories.length}</div>
                    </div>
                </div>
            </div>
        </div>
    `;

    return filterBar;
}

/**
 * Initialise la page des leçons
 */
function initLessonsPage() {
    console.log('Initialisation de la page Leçons');

    // Trouver le conteneur principal
    const mainContainer = document.querySelector('main .container') || document.querySelector('main');
    if (!mainContainer) {
        console.error('Conteneur principal non trouvé');
        return;
    }

    // Créer la structure de la page
    const pageStructure = document.createElement('div');
    pageStructure.innerHTML = `
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-4">📚 Leçons JavaScript</h1>
            <p class="text-lg text-base-content/70 max-w-3xl">
                Apprenez JavaScript étape par étape avec nos leçons structurées.
                Chaque leçon est conçue pour vous faire progresser de manière logique et efficace.
            </p>
        </div>

        <div id="lessons-filter-container"></div>

        <div id="lessons-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Les cartes leçons seront générées ici -->
        </div>
    `;

    // Ajouter la structure à la page
    mainContainer.appendChild(pageStructure);

    // Ajouter la barre de filtres
    const filterContainer = document.getElementById('lessons-filter-container');
    if (filterContainer) {
        filterContainer.appendChild(createLessonsFilterBar());
    }

    // Rendre la liste des leçons
    renderLessonsList();

    // Charger le progrès depuis localStorage
    loadLessonsProgress();
}

/**
 * Charge le progrès des leçons depuis localStorage
 */
function loadLessonsProgress() {
    const progress = JSON.parse(localStorage.getItem('lessons-progress') || '{}');

    // Mettre à jour le statut des leçons selon le progrès
    lessonsData.forEach(lesson => {
        if (progress[lesson.id]) {
            const lessonProgress = progress[lesson.id];
            if (lessonProgress.status === 'completed') {
                lesson.status = 'completed';
            }
        }
    });
}

/**
 * Fonction utilitaire pour ajouter une nouvelle leçon
 * @param {Object} newLesson - Les données de la nouvelle leçon
 */
function addNewLesson(newLesson) {
    // Validation basique
    const requiredFields = ['id', 'title', 'description', 'difficulty', 'duration', 'topics', 'file', 'category'];
    const missingFields = requiredFields.filter(field => !newLesson[field]);

    if (missingFields.length > 0) {
        console.error('Champs manquants pour la nouvelle leçon:', missingFields);
        return false;
    }

    // Vérifier que l'ID est unique
    if (lessonsData.find(lesson => lesson.id === newLesson.id)) {
        console.error('Une leçon avec cet ID existe déjà:', newLesson.id);
        return false;
    }

    // Ajouter les valeurs par défaut
    const lessonWithDefaults = {
        status: 'available',
        badge: null,
        order: lessonsData.length + 1,
        ...newLesson
    };

    // Ajouter à la liste
    lessonsData.push(lessonWithDefaults);

    // Re-rendre la liste
    renderLessonsList();

    console.log('Nouvelle leçon ajoutée:', newLesson.id);
    return true;
}

/**
 * Fonction utilitaire pour créer rapidement une nouvelle leçon
 * Utilisez cette fonction dans la console pour ajouter facilement de nouvelles leçons
 *
 * Exemple d'utilisation :
 * createQuickLesson('async-await', 'Async/Await', 'Maîtriser la programmation asynchrone', 'Avancé', '90 min', ['Async', 'Promises'], 'Avancé')
 */
function createQuickLesson(id, title, description, difficulty = 'Débutant', duration = '45 min', topics = [], category = 'Fondamentaux') {
    const newLesson = {
        id: id,
        title: title,
        description: description,
        difficulty: difficulty,
        duration: duration,
        topics: topics,
        file: `${id}.html`,
        status: 'coming-soon',
        badge: 'Nouveau',
        category: category
    };

    return addNewLesson(newLesson);
}

// Rendre les fonctions globales pour pouvoir les utiliser dans le HTML
window.openLesson = openLesson;
window.filterLessons = filterLessons;
window.addNewLesson = addNewLesson;
window.createQuickLesson = createQuickLesson;

// Initialiser la page des leçons si on est sur la page index des leçons
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si on est sur la page index des leçons
    if (window.location.pathname.includes('/lessons/') &&
        (window.location.pathname.endsWith('/') || window.location.pathname.includes('index.html'))) {
        initLessonsPage();
    }
});

// Afficher des informations utiles dans la console
console.log(`
📚 Système de leçons automatisé initialisé !

📖 Leçons disponibles: ${lessonsData.length}
🏷️ Catégories: ${[...new Set(lessonsData.map(l => l.category))].join(', ')}

🛠️ Fonctions utilitaires disponibles:
- createQuickLesson(id, title, description, difficulty, duration, topics, category) : Créer rapidement une nouvelle leçon
- addNewLesson(lessonObject) : Ajouter une leçon avec un objet complet
- filterLessons(type, value) : Filtrer les leçons

💡 Exemple pour ajouter une nouvelle leçon:
createQuickLesson('ma-lecon', 'Ma Nouvelle Leçon', 'Description de la leçon', 'Débutant', '30 min', ['JavaScript'], 'Fondamentaux')
`);
