<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Les Custom Elements | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">

            <div id="navbar-container"></div>

            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">

                <section class="text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">Les Custom Elements</h1>
                    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
                        Les Custom Elements vous permettent de créer vos propres balises HTML, avec leur propre logique
                        et leur propre style. C'est l'une des spécifications des **Web Components** et une excellente
                        manière de créer des composants réutilisables et isolés.
                    </p>
                </section>

                <section class="mb-12">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">👉 Pourquoi utiliser des Custom Elements ?</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>**Réutilisabilité** : Créez des composants complexes (comme un carrousel ou une
                                    carte utilisateur) et réutilisez-les facilement partout dans votre application.</li>
                                <li>**Encapsulation** : Le style et la logique du composant sont encapsulés et ne
                                    polluent pas le reste de la page.</li>
                                <li>**Standard Web** : Contrairement aux frameworks (React, Vue), les Custom Elements
                                    sont une spécification native du navigateur.</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="mb-12">
                    <div class="card bg-primary text-primary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">⚡ Créer et utiliser un Custom Element</h2>
                            <p class="mb-4">
                                La création se fait en deux étapes : définir la classe du composant et l'enregistrer
                                dans le navigateur.
                            </p>
                            <div class="space-y-6">
                                <div>
                                    <h3 class="font-bold text-lg mb-2">1. Définir la classe du composant</h3>
                                    <p>
                                        La classe doit étendre `HTMLElement`. Le `constructor` est le point d'entrée
                                        pour initialiser le composant.
                                    </p>
                                    <pre class="mockup-code text-left">
                    <code>
class CompteurButton extends HTMLElement {
  constructor() {
    super(); // Toujours appeler super() en premier
    this.compteur = 0;
    this.addEventListener('click', () => {
      this.compteur++;
      this.textContent = `Clics : ${this.compteur}`;
    });
  }

  // Lifecycle callback: appelé lorsque l'élément est ajouté au DOM
  connectedCallback() {
    this.textContent = `Clics : ${this.compteur}`;
  }
}
                    </code>
                  </pre>
                                </div>

                                <div>
                                    <h3 class="font-bold text-lg mb-2">2. Enregistrer le Custom Element</h3>
                                    <p>
                                        On utilise la méthode `customElements.define()` en lui donnant un nom de balise
                                        et la classe correspondante.
                                    </p>
                                    <pre class="mockup-code text-left">
                    <code>
// Le nom doit contenir un tiret (-)
customElements.define('compteur-button', CompteurButton);
                    </code>
                  </pre>
                                </div>

                                <div>
                                    <h3 class="font-bold text-lg mb-2">3. Utiliser la balise dans votre HTML</h3>
                                    <p>
                                        Maintenant, vous pouvez simplement utiliser votre nouvelle balise dans votre
                                        HTML.
                                    </p>
                                    <pre class="mockup-code text-left">
                    <code>
&lt;!-- index.html --&gt;
&lt;!-- L'attribut 'is' peut être utilisé pour étendre des éléments existants --&gt;
&lt;button is="compteur-button"&gt;&lt;/button&gt;
                    </code>
                  </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section>
                    <div class="card bg-neutral text-neutral-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">✅ Bonnes pratiques et cas d'utilisation</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>Le nom de la balise doit obligatoirement contenir un **tiret** (ex: `ma-balise`).
                                </li>
                                <li>Utilisez le **Shadow DOM** (`this.attachShadow({ mode: 'open' })`) pour isoler le
                                    style et le markup de votre composant.</li>
                                <li>Les Custom Elements sont parfaits pour des composants atomiques ou de petits widgets
                                    réutilisables, comme un menu déroulant, un bouton de partage, un slider, etc.</li>
                                <li>Ils peuvent être utilisés avec n'importe quel framework ou bibliothèque, ou même en
                                    JavaScript pur.</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>

            <div id="footer-container"></div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>

</html>