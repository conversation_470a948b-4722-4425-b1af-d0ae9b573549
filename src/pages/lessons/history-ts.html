<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>L'Histoire de JavaScript | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">

            <div id="navbar-container"></div>

            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">

                <!-- Hero Section -->
                <section class="text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">L'histoire de TypeScript : De ses origines à son adoption</h1>
                    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
                        TypeScript, le sur-ensemble typé de JavaScript, a profondément transformé le développement web.
                        Découvrez
                        son histoire, depuis sa création chez Microsoft jusqu'à sa position dominante aujourd'hui.
                    </p>
                </section>

                <!-- Origines et motivations -->
                <section class="mb-12">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">👉 Pourquoi TypeScript a-t-il été créé ?</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>
                                    **Besoin d'évolutivité :** Chez Microsoft, JavaScript posait de gros problèmes pour
                                    les grands
                                    projets avec des milliers de lignes de code.
                                </li>
                                <li>
                                    **Outils de développement :** L'absence de typage statique rendait difficile
                                    l'autocomplétion, le
                                    refactoring et la détection des erreurs avant l'exécution.
                                </li>
                                <li>
                                    **Simplicité :** L'idée était de rendre JavaScript plus sûr et plus facile à
                                    maintenir sans
                                    complexifier la syntaxe ni l'écosystème.
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Chronologie des faits marquants -->
                <section class="mb-12">
                    <div class="card bg-primary text-primary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">⚡ Chronologie des faits marquants</h2>
                            <ol class="list-decimal list-inside space-y-4 mt-3">
                                <li>
                                    **2012 : Lancement public.** Le 1er octobre, Microsoft dévoile TypeScript 1.0. Son
                                    architecte en chef
                                    est le célèbre Anders Hejlsberg, créateur de C# et Delphi.
                                </li>
                                <li>
                                    **2014 : TypeScript 1.3** introduit les types "protected" et "private".
                                </li>
                                <li>
                                    **2015 : Intégration dans Angular 2.** Le framework Angular de Google adopte
                                    TypeScript comme langage
                                    principal, ce qui propulse sa popularité.
                                </li>
                                <li>
                                    **2016 : TypeScript 2.0.** Une version majeure qui apporte une amélioration de
                                    l'inférence de type et le
                                    `null` et `undefined` de façon stricte.
                                </li>
                                <li>
                                    **2018 : TypeScript 3.0.** Ajout des tuples et des références de projet, renforçant
                                    la capacité à
                                    gérer des bases de code plus grandes.
                                </li>
                                <li>
                                    **2020 : TypeScript 4.0.** Introduction des tuples avec étiquettes et du JSX
                                    fragment.
                                </li>
                                <li>
                                    **Aujourd'hui : Écosystème riche.** TypeScript est désormais un standard de
                                    l'industrie, avec un
                                    soutien fort de la communauté et une intégration parfaite avec des outils comme VS
                                    Code,
                                    React et Vue.js.
                                </li>
                            </ol>
                        </div>
                    </div>
                </section>

                <!-- Bonnes pratiques -->
                <section>
                    <div class="card bg-neutral text-neutral-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">✅ Les raisons de son succès</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>
                                    **Productivité accrue :** Le typage statique permet de détecter les erreurs tôt dans
                                    le cycle de
                                    développement, ce qui réduit les bugs et les coûts de maintenance.
                                </li>
                                <li>
                                    **Lisibilité du code :** Les types explicites rendent le code plus facile à lire et
                                    à comprendre,
                                    surtout dans les équipes.
                                </li>
                                <li>
                                    **Adoption des standards :** TypeScript est un superset de JavaScript. Cela signifie
                                    que tout le code
                                    JavaScript est du TypeScript valide.
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>

            <div id="footer-container"></div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>

</html>