<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Leçon DOM Events JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div id="navbar-container"></div>


        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">

          <!-- Hero Section -->
          <div class="hero bg-gradient-to-r from-primary to-secondary rounded-box mb-8 text-primary-content">
            <div class="hero-content text-center py-12">
              <div class="max-w-md">
                <h1 class="mb-5 text-5xl font-bold">🎯 DOM Events</h1>
                <p class="mb-5 text-lg">
                  Maîtrisez les événements JavaScript avec addEventListener()
                  et créez des interfaces interactives !
                </p>
                <div class="badge badge-accent badge-lg">Niveau : Intermédiaire</div>
              </div>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="mb-8">
            <div class="flex justify-between text-sm mb-2">
              <span>Progression de la leçon</span>
              <span id="progress-text">0%</span>
            </div>
            <progress class="progress progress-primary w-full" value="0" max="100" id="lesson-progress"></progress>
          </div>

          <!-- Lesson Content -->
          <div class="space-y-8">

            <!-- Section 1: Introduction -->
            <div class="card bg-base-100 shadow-xl lesson-section" data-section="1">
              <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                  <span class="badge badge-primary mr-2">1</span>
                  Qu'est-ce qu'un DOM Event ?
                </h2>

                <div class="prose max-w-none">
                  <p class="text-lg mb-4">
                    Les <strong>DOM Events</strong> sont des signaux qui indiquent qu'une action s'est produite
                    dans le navigateur : clic de souris, frappe de clavier, chargement de page, etc.
                  </p>

                  <div class="alert alert-info mb-4">
                    <svg class="stroke-current shrink-0 w-6 h-6" fill="none" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span><strong>addEventListener()</strong> est la méthode moderne et recommandée pour gérer les événements !</span>
                  </div>
                </div>

                <!-- Interactive Demo 1 -->
                <div class="bg-base-200 p-4 rounded-lg">
                  <h3 class="font-bold mb-3">🎮 Démo Interactive :</h3>
                  <button class="btn btn-primary" id="demo-btn-1">Cliquez-moi !</button>
                  <div class="mt-3 p-3 bg-base-300 rounded hidden" id="demo-result-1">
                    <span class="text-success">✅ Événement 'click' détecté !</span>
                  </div>
                </div>

                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline" onclick="markSectionComplete(1)">Section terminée ✓</button>
                </div>
              </div>
            </div>

            <!-- Section 2: addEventListener Syntax -->
            <div class="card bg-base-100 shadow-xl lesson-section" data-section="2">
              <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                  <span class="badge badge-secondary mr-2">2</span>
                  Syntaxe d'addEventListener()
                </h2>

                <div class="mockup-code mb-4">
                  <pre data-prefix="1"><code>element.addEventListener(event, function, options);</code></pre>
                  <pre data-prefix="2"><code></code></pre>
                  <pre data-prefix="3"><code>// Exemple :</code></pre>
                  <pre data-prefix="4"><code>button.addEventListener('click', function() {</code></pre>
                  <pre data-prefix="5"><code>    console.log('Bouton cliqué !');</code></pre>
                  <pre data-prefix="6"><code>});</code></pre>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div class="card bg-primary text-primary-content">
                    <div class="card-body text-center">
                      <h3 class="card-title text-sm">event</h3>
                      <p class="text-xs">Type d'événement<br/>(click, keydown, etc.)</p>
                    </div>
                  </div>
                  <div class="card bg-secondary text-secondary-content">
                    <div class="card-body text-center">
                      <h3 class="card-title text-sm">function</h3>
                      <p class="text-xs">Fonction à exécuter<br/>quand l'événement se produit</p>
                    </div>
                  </div>
                  <div class="card bg-accent text-accent-content">
                    <div class="card-body text-center">
                      <h3 class="card-title text-sm">options</h3>
                      <p class="text-xs">Paramètres optionnels<br/>(once, passive, etc.)</p>
                    </div>
                  </div>
                </div>

                <!-- Interactive Demo 2 -->
                <div class="bg-base-200 p-4 rounded-lg">
                  <h3 class="font-bold mb-3">🎮 Testez différents événements :</h3>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <button class="btn btn-sm btn-outline" data-event="click">Click</button>
                    <button class="btn btn-sm btn-outline" data-event="dblclick">Double Click</button>
                    <input type="text" class="input input-sm input-bordered" placeholder="Tapez ici..." data-event="keyup" id="input-text">
                    <div class="btn btn-sm btn-outline cursor-pointer" data-event="mouseenter">Hover me</div>
                  </div>
                  <div class="mt-3 p-3 bg-base-300 rounded">
                    <div class="text-sm font-mono" id="event-log">Aucun événement détecté</div>
                    <div id="renderKey" class="text-xs mt-2 text-primary"></div>
                  </div>
                </div>

                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline" onclick="markSectionComplete(2)">Section terminée ✓</button>
                </div>
              </div>
            </div>

            <!-- Section 3: addEventListener vs onClick -->
            <div class="card bg-base-100 shadow-xl lesson-section" data-section="3">
              <div class="card-body">
                <h2 class="card-title text-2xl mb-4">
                  <span class="badge badge-warning mr-2">3</span>
                  addEventListener vs onClick - Pourquoi éviter onClick ?
                </h2>

                <!-- Comparaison -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <div class="card bg-success text-success-content">
                    <div class="card-body">
                      <h3 class="card-title text-lg">✅ addEventListener() - Recommandé</h3>
                      <div class="mockup-code text-xs">
                        <pre><code>button.addEventListener('click', function() {</code></pre>
                        <pre><code>    console.log('Moderne et flexible!');</code></pre>
                        <pre><code>});</code></pre>
                      </div>
                      <ul class="text-sm mt-2 space-y-1">
                        <li>• Plusieurs listeners sur le même événement</li>
                        <li>• Séparation HTML/JS</li>
                        <li>• Options avancées (once, passive)</li>
                        <li>• Facile à supprimer avec removeEventListener</li>
                      </ul>
                    </div>
                  </div>

                  <div class="card bg-error text-error-content">
                    <div class="card-body">
                      <h3 class="card-title text-lg">❌ onclick - À éviter</h3>
                      <div class="mockup-code text-xs">
                        <pre><code>&lt;button onclick="maFonction()"&gt;</code></pre>
                        <pre><code>// ou</code></pre>
                        <pre><code>button.onclick = function() { ... }</code></pre>
                      </div>
                      <ul class="text-sm mt-2 space-y-1">
                        <li>• Un seul listener par événement</li>
                        <li>• Mélange HTML/JS</li>
                        <li>• Pas d'options avancées</li>
                        <li>• Difficile à maintenir</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Démo comparative -->
                <div class="bg-base-200 p-4 rounded-lg mb-4">
                  <h3 class="font-bold mb-3">🎮 Comparaison en action :</h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 class="font-semibold text-success mb-2">addEventListener (multiple listeners)</h4>
                      <button class="btn btn-success btn-sm w-full mb-2" id="good-practice-btn">
                        Cliquez-moi ! (addEventListener)
                      </button>
                      <div class="text-xs bg-base-300 p-2 rounded" id="good-practice-log">
                        Aucun clic détecté
                      </div>
                    </div>
                    <div>
                      <h4 class="font-semibold text-error mb-2">onclick (un seul listener)</h4>
                      <button class="btn btn-error btn-sm w-full mb-2" id="bad-practice-btn">
                        Cliquez-moi ! (onclick)
                      </button>
                      <div class="text-xs bg-base-300 p-2 rounded" id="bad-practice-log">
                        Aucun clic détecté
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-actions justify-end mt-4">
                  <button class="btn btn-outline" onclick="markSectionComplete(3)">Section terminée ✓</button>
                </div>
              </div>
            </div>

            <!-- Quiz Section -->
            <div class="card bg-gradient-to-r from-success to-info text-white shadow-xl" id="quiz-section">
              <div class="card-body">
                <h2 class="card-title text-2xl mb-4">🧠 Quiz Final</h2>
                <div id="quiz-container">
                  <div class="text-center">
                    <p class="mb-4">Terminez toutes les sections pour débloquer le quiz !</p>
                    <div class="loading loading-spinner loading-lg"></div>
                  </div>
                </div>
              </div>
            </div>

          </div>

        </main>

        <!-- Footer -->
        <div id="footer-container"></div>

      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module" src="/src/pages/lessons/lessons.js"></script>

    <!-- Script interactif pour la leçon DOM Events -->
    <script>
      // État de progression de la leçon
      let completedSections = new Set();
      const totalSections = 3;

      // Mise à jour de la barre de progression
      function updateProgress() {
        const progress = (completedSections.size / totalSections) * 100;
        document.getElementById('lesson-progress').value = progress;
        document.getElementById('progress-text').textContent = Math.round(progress) + '%';

        if (progress === 100) {
          showQuiz();
        }
      }

      // Marquer une section comme terminée
      function markSectionComplete(sectionNumber) {
        completedSections.add(sectionNumber);
        const section = document.querySelector(`[data-section="${sectionNumber}"]`);
        section.classList.add('opacity-75');
        section.querySelector('.card-actions button').classList.add('btn-success');
        section.querySelector('.card-actions button').textContent = 'Terminé ✅';
        updateProgress();
      }

      // Initialisation des démos interactives
      document.addEventListener('DOMContentLoaded', function() {

        // Démo 1 : Bouton simple
        const demoBtn1 = document.getElementById('demo-btn-1');
        const demoResult1 = document.getElementById('demo-result-1');

        demoBtn1.addEventListener('click', function() {
          demoResult1.classList.remove('hidden');
          demoBtn1.textContent = 'Événement détecté ! 🎉';
          demoBtn1.classList.add('btn-success');

          setTimeout(() => {
            demoBtn1.textContent = 'Cliquez-moi !';
            demoBtn1.classList.remove('btn-success');
            demoResult1.classList.add('hidden');
          }, 2000);
        });

        // Démo 2 : Différents types d'événements
        const eventLog = document.getElementById('event-log');
        const renderKey = document.getElementById('renderKey');

        // Boutons click et double-click
        document.querySelectorAll('[data-event="click"]').forEach(btn => {
          btn.addEventListener('click', function() {
            logEvent('click', 'Bouton cliqué !');
          });
        });

        document.querySelectorAll('[data-event="dblclick"]').forEach(btn => {
          btn.addEventListener('dblclick', function() {
            logEvent('dblclick', 'Double-clic détecté !');
          });
        });

        // Input keyup (intégration avec l'existant)
        const inputText = document.getElementById('input-text');
        inputText.addEventListener('keyup', function(e) {
          logEvent('keyup', `Touche "${e.key}" relâchée`);
          renderKey.textContent = `Dernière touche: ${e.key} (Code: ${e.keyCode})`;
        });

        // Hover
        document.querySelectorAll('[data-event="mouseenter"]').forEach(element => {
          element.addEventListener('mouseenter', function() {
            logEvent('mouseenter', 'Souris entrée dans la zone');
            this.classList.add('btn-primary');
          });

          element.addEventListener('mouseleave', function() {
            logEvent('mouseleave', 'Souris sortie de la zone');
            this.classList.remove('btn-primary');
          });
        });

        // Démo 3 : Comparaison addEventListener vs onclick

        // addEventListener (multiple listeners)
        const goodBtn = document.getElementById('good-practice-btn');
        const goodLog = document.getElementById('good-practice-log');
        let goodClickCount = 0;

        // Premier listener
        goodBtn.addEventListener('click', function() {
          goodClickCount++;
          goodLog.innerHTML = `Listener 1: Clic #${goodClickCount}`;
        });

        // Deuxième listener (possible avec addEventListener)
        goodBtn.addEventListener('click', function() {
          goodLog.innerHTML += `<br>Listener 2: Animation déclenchée`;
          goodBtn.classList.add('animate-pulse');
          setTimeout(() => goodBtn.classList.remove('animate-pulse'), 1000);
        });

        // Troisième listener
        goodBtn.addEventListener('click', function() {
          goodLog.innerHTML += `<br>Listener 3: Log envoyé`;
        });

        // onclick (un seul listener)
        const badBtn = document.getElementById('bad-practice-btn');
        const badLog = document.getElementById('bad-practice-log');
        let badClickCount = 0;

        // Premier onclick
        badBtn.onclick = function() {
          badClickCount++;
          badLog.innerHTML = `onclick 1: Clic #${badClickCount}`;
        };

        // Deuxième onclick (écrase le premier !)
        badBtn.onclick = function() {
          badClickCount++;
          badLog.innerHTML = `onclick 2: Le premier a été écrasé ! Clic #${badClickCount}`;
        };

        // Animation d'entrée pour les sections
        const sections = document.querySelectorAll('.lesson-section');
        sections.forEach((section, index) => {
          section.style.opacity = '0';
          section.style.transform = 'translateY(20px)';

          setTimeout(() => {
            section.style.transition = 'all 0.6s ease-out';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
          }, index * 200);
        });
      });

      // Fonction pour logger les événements
      function logEvent(eventType, message) {
        const eventLog = document.getElementById('event-log');
        const timestamp = new Date().toLocaleTimeString();
        eventLog.innerHTML = `
          <div class="text-primary font-bold">[${timestamp}] ${eventType}</div>
          <div>${message}</div>
        `;

        // Animation flash
        eventLog.parentElement.classList.add('ring-2', 'ring-primary');
        setTimeout(() => {
          eventLog.parentElement.classList.remove('ring-2', 'ring-primary');
        }, 500);
      }

      // Quiz final
      const quizQuestions = [
        {
          question: "Quelle est la syntaxe correcte pour addEventListener ?",
          options: [
            "element.addEventListener(event, function)",
            "element.addEvent(event, function)",
            "element.on(event, function)",
            "element.listen(event, function)"
          ],
          correct: 0
        },
        {
          question: "Pourquoi préférer addEventListener à onclick ?",
          options: [
            "Plus rapide à exécuter",
            "Permet plusieurs listeners sur le même événement",
            "Plus facile à écrire",
            "Fonctionne sur tous les navigateurs"
          ],
          correct: 1
        },
        {
          question: "Quel événement se déclenche quand on tape au clavier ?",
          options: ["press", "click", "keyup", "input"],
          correct: 2
        }
      ];

      function showQuiz() {
        const quizContainer = document.getElementById('quiz-container');
        let currentQuestion = 0;
        let score = 0;

        function renderQuestion() {
          const question = quizQuestions[currentQuestion];
          quizContainer.innerHTML = `
            <div class="mb-4">
              <h3 class="text-lg font-bold mb-3">Question ${currentQuestion + 1}/${quizQuestions.length}</h3>
              <p class="mb-4">${question.question}</p>
              <div class="space-y-2">
                ${question.options.map((option, index) => `
                  <button class="btn btn-outline btn-block text-left quiz-option" data-index="${index}">
                    ${String.fromCharCode(65 + index)}. ${option}
                  </button>
                `).join('')}
              </div>
            </div>
          `;

          // Ajouter les event listeners pour les options
          document.querySelectorAll('.quiz-option').forEach(btn => {
            btn.addEventListener('click', function() {
              const selectedIndex = parseInt(this.dataset.index);
              const isCorrect = selectedIndex === question.correct;

              // Désactiver tous les boutons
              document.querySelectorAll('.quiz-option').forEach(b => b.disabled = true);

              // Colorer la réponse
              if (isCorrect) {
                this.classList.add('btn-success');
                score++;
              } else {
                this.classList.add('btn-error');
                document.querySelectorAll('.quiz-option')[question.correct].classList.add('btn-success');
              }

              setTimeout(() => {
                currentQuestion++;
                if (currentQuestion < quizQuestions.length) {
                  renderQuestion();
                } else {
                  showResults();
                }
              }, 1500);
            });
          });
        }

        function showResults() {
          const percentage = Math.round((score / quizQuestions.length) * 100);
          let message, badgeClass;

          if (percentage >= 80) {
            message = "Excellent ! Vous maîtrisez les DOM Events ! 🎉";
            badgeClass = "badge-success";
          } else if (percentage >= 60) {
            message = "Bien joué ! Continuez à pratiquer ! 👍";
            badgeClass = "badge-warning";
          } else {
            message = "Pas mal ! Relisez la leçon et réessayez ! 📚";
            badgeClass = "badge-error";
          }

          quizContainer.innerHTML = `
            <div class="text-center">
              <h3 class="text-2xl font-bold mb-4">Résultats du Quiz</h3>
              <div class="text-6xl mb-4">${percentage}%</div>
              <div class="badge ${badgeClass} badge-lg mb-4">${score}/${quizQuestions.length} correct</div>
              <p class="text-lg mb-6">${message}</p>
              <button class="btn btn-primary" onclick="location.reload()">Recommencer la leçon</button>
            </div>
          `;
        }

        renderQuestion();
      }

      // Exposer les fonctions globalement
      window.markSectionComplete = markSectionComplete;
    </script>
  </body>
</html>
