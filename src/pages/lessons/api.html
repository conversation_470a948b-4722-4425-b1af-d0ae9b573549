<!doctype html>
<html lang="fr">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="/src/style.css">
  <title>Leçon : API et Fetch</title>
</head>

<body class="min-h-screen">
  <div class="drawer min-h-screen">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col min-h-screen">
      <!-- Navbar -->
      <div id="navbar-container"></div>

      <!-- Main Content -->
      <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <!-- En-tête -->
        <h1 class="text-4xl font-bold mb-4">Découvrir les API avec JavaScript</h1>
        <p class="text-lg text-base-content/70 max-w-2xl mb-6">
          Apprenez à utiliser <span class="font-semibold">Fetch API</span> pour récupérer des données depuis un serveur. 
          Nous allons explorer <code>then() / catch()</code> et <code>async / await</code>.
        </p>

        <!-- Section : Introduction -->
        <div class="card bg-base-100 shadow-xl mb-6">
          <div class="card-body">
            <h2 class="card-title">Qu’est-ce qu’une API ?</h2>
            <p>
              Une API (Application Programming Interface) permet à votre application de communiquer avec des services externes 
              (par exemple : météo, films, bases de données).
            </p>
          </div>
        </div>

        <!-- Section : Exemple Fetch -->
        <div class="card bg-base-200 shadow-lg mb-6">
          <div class="card-body">
            <h2 class="card-title">Exemple simple avec Fetch</h2>
            <p>
              Voici comment récupérer des données avec <code>fetch()</code> et traiter la réponse avec 
              <code>.then()</code> et <code>.catch()</code>.
            </p>
            <pre class="mockup-code"><code>
// Exemple avec then() / catch()
fetch("https://jsonplaceholder.typicode.com/posts/1")
  .then(response => response.json())
  .then(data => {
    console.log("Post récupéré :", data);
  })
  .catch(error => {
    console.error("Erreur :", error);
  });
            </code></pre>
          </div>
        </div>

        <!-- Section : Exemple async/await -->
        <div class="card bg-primary text-primary-content shadow-xl mb-6">
          <div class="card-body">
            <h2 class="card-title">Avec async / await</h2>
            <p>
              Utiliser <code>async/await</code> rend le code plus lisible et proche du style synchrone.
            </p>
            <pre class="mockup-code"><code>
// Exemple avec async/await
async function getPost() {
  try {
    const response = await fetch("https://jsonplaceholder.typicode.com/posts/1");
    const data = await response.json();
    console.log("Post récupéré :", data);
  } catch (error) {
    console.error("Erreur :", error);
  }
}

getPost();
            </code></pre>
          </div>
        </div>

        <!-- Section : Exercice -->
        <div class="card bg-accent text-accent-content shadow-lg">
          <div class="card-body">
            <h2 class="card-title">🚀 Exercice pratique</h2>
            <p>
              Récupérez une liste d’utilisateurs depuis l’API publique 
              <a href="https://jsonplaceholder.typicode.com/users" class="link">JSONPlaceholder</a>.  
              Affichez leur nom et email dans une liste HTML.
            </p>
          </div>
        </div>

      </main>

      <!-- Footer -->
      <div id="footer-container"></div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>
