<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Les Spread Operators | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">

            <div id="navbar-container"></div>

            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">

                <section class="text-center mb-10">
                    <h1 class="text-4xl font-bold mb-4">Les Spread Operators (`...`)</h1>
                    <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
                        Introduits avec ES6, le `spread operator` (`...`) permet de "décomposer" un tableau ou un objet
                        en une liste d'éléments individuels. C'est un outil très puissant pour la manipulation de
                        données.
                    </p>
                </section>

                <section class="mb-12">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">👉 Pourquoi utiliser les Spread Operators ?</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>Fusionner des tableaux ou des objets de manière concise.</li>
                                <li>Créer des copies de tableaux ou d'objets sans modifier l'original.</li>
                                <li>Passer les éléments d'un tableau comme arguments d'une fonction.</li>
                                <li>Ajouter facilement de nouveaux éléments à un tableau ou de nouvelles propriétés à un
                                    objet.</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="mb-12">
                    <div class="card bg-primary text-primary-content shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">⚡ Utilisation avec les tableaux</h2>
                            <div class="space-y-6">
                                <div>
                                    <h3 class="font-bold text-lg mb-2">Copier un tableau</h3>
                                    <p>Permet de créer une copie superficielle (`shallow copy`), ce qui évite de
                                        modifier l'original.</p>
                                    <pre class="mockup-code text-left">
                    <code>
const original = [1, 2, 3];
const copie = [...original];

copie.push(4);

console.log(original); // [1, 2, 3]
console.log(copie);    // [1, 2, 3, 4]
                    </code>
                  </pre>
                                </div>

                                <div>
                                    <h3 class="font-bold text-lg mb-2">Combiner des tableaux</h3>
                                    <p>Fusionnez plusieurs tableaux en un seul, de manière simple et lisible.</p>
                                    <pre class="mockup-code text-left">
                    <code>
const legumes = ['carotte', 'brocoli'];
const fruits = ['pomme', 'banane'];

const courses = [...legumes, ...fruits];
console.log(courses); // ['carotte', 'brocoli', 'pomme', 'banane']
                    </code>
                  </pre>
                                </div>

                                <div>
                                    <h3 class="font-bold text-lg mb-2">Ajouter un élément</h3>
                                    <p>Ajoutez un élément à un tableau sans utiliser `push` ou `unshift`.</p>
                                    <pre class="mockup-code text-left">
                    <code>
const legumes = ['carotte', 'brocoli'];

const legumesAjoutes = ['poireau', ...legumes];
console.log(legumesAjoutes); // ['poireau', 'carotte', 'brocoli']
                    </code>
                  </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="mb-12">
                    <div class="card bg-base-100 shadow-lg">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">🌱 Utilisation avec les objets</h2>
                            <p class="mb-4">
                                Le `spread operator` fonctionne aussi avec les objets pour créer des copies ou les
                                fusionner.
                            </p>
                            <div class="space-y-6">
                                <div>
                                    <h3 class="font-bold text-lg mb-2">Copier et fusionner des objets</h3>
                                    <p>Créez une nouvelle instance d'un objet en y ajoutant ou en écrasant des
                                        propriétés.</p>
                                    <pre class="mockup-code text-left">
                    <code>
const utilisateur = { nom: 'Alice', age: 30 };
const infosVille = { ville: 'Paris', pays: 'France' };

const utilisateurComplet = { ...utilisateur, ...infosVille };
console.log(utilisateurComplet);
// { nom: 'Alice', age: 30, ville: 'Paris', pays: 'France' }

// Écraser une propriété
const utilisateurMajeur = { ...utilisateur, age: 25 };
console.log(utilisateurMajeur);
// { nom: 'Alice', age: 25 }
                    </code>
                  </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section>
                    <div class="card bg-neutral text-neutral-content shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl">✅ Bonnes pratiques</h2>
                            <ul class="list-disc list-inside space-y-2 mt-3">
                                <li>Utilisez le `spread operator` pour créer des copies de tableaux et d'objets, c'est
                                    la méthode moderne et la plus concise.</li>
                                <li>Il est préférable de l'utiliser pour fusionner des objets plutôt que
                                    `Object.assign()`.</li>
                                <li>Il ne crée qu'une **copie superficielle**. Si les tableaux ou objets imbriqués
                                    doivent aussi être copiés, il faudra une méthode plus profonde comme un `deep
                                    clone`.</li>
                            </ul>
                        </div>
                    </div>
                </section>
            </main>

            <div id="footer-container"></div>
        </div>
    </div>

    <script type="module" src="/src/main.js"></script>
</body>

</html>