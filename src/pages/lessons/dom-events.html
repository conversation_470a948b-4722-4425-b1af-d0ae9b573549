<!doctype html>
<html lang="fr">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="/src/style.css">
  <title>Gestion des événements DOM | Jefff303.js</title>
</head>

<body class="min-h-screen bg-base-200">
  <div class="drawer min-h-screen">
    <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
    <div class="drawer-content flex flex-col min-h-screen">
      
      <!-- Navbar -->
      <div id="navbar-container"></div>

      <!-- Main Content -->
      <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-10">
        
        <!-- Hero Section -->
        <section class="text-center mb-10">
          <h1 class="text-4xl font-bold mb-4">🎯 Gestion des événements dans le DOM</h1>
          <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
            Apprenez à gérer les interactions des utilisateurs avec <code>addEventListener</code>, 
            la méthode moderne pour écouter les événements dans JavaScript.
          </p>
        </section>

        <!-- Qu'est-ce qu'un event DOM ? -->
        <section class="mb-12">
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h2 class="card-title text-2xl">📌 Qu’est-ce qu’un événement DOM ?</h2>
              <p class="mt-3">
                Les événements DOM sont des signaux envoyés par le navigateur quand l’utilisateur interagit avec une page :
                un clic, une touche du clavier, ou même le chargement de la page.
              </p>
              <ul class="list-disc list-inside space-y-2 mt-3">
                <li><code>click</code> → quand on clique sur un élément</li>
                <li><code>keyup</code> / <code>keydown</code> → quand on appuie sur une touche</li>
                <li><code>submit</code> → quand on envoie un formulaire</li>
                <li><code>mouseover</code> / <code>mouseout</code> → passage de la souris</li>
                <li><code>load</code> → quand la page est entièrement chargée</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- addEventListener -->
        <section class="mb-12">
          <div class="card bg-primary text-primary-content shadow-lg">
            <div class="card-body">
              <h2 class="card-title text-2xl">⚡ Utiliser <code>addEventListener</code></h2>
              <p class="mb-4">
                La méthode <code>addEventListener</code> permet d’écouter un événement sur un élément du DOM 
                et d’exécuter une fonction quand celui-ci se produit.
              </p>
              <pre class="mockup-code text-left">
                <code>// Sélectionner un bouton
const btn = document.querySelector("#myBtn");

// Ajouter un écouteur d’événement
btn.addEventListener("click", () => {
  alert("Bouton cliqué !");
});</code>
              </pre>
              <p class="mt-4">✅ Avantages :</p>
              <ul class="list-disc list-inside mt-2">
                <li>On peut attacher plusieurs événements sur le même élément.</li>
                <li>Séparation du HTML et du JavaScript (plus propre).</li>
                <li>Compatible avec la plupart des navigateurs modernes.</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- onclick (pour comparaison) -->
        <section class="mb-12">
          <div class="card bg-secondary text-secondary-content shadow-lg">
            <div class="card-body">
              <h2 class="card-title text-2xl">🤔 Et <code>onclick</code> alors ?</h2>
              <p class="mb-4">
                Avant <code>addEventListener</code>, on utilisait souvent <code>onclick</code> :
              </p>
              <pre class="mockup-code text-left">
                <code>// Sélectionner un bouton
const btn = document.querySelector("#myBtn");

// Version onclick
btn.onclick = () => {
  alert("Bouton cliqué !");
};</code>
              </pre>
              <p class="mt-4">⚠️ Inconvénients :</p>
              <ul class="list-disc list-inside mt-2">
                <li>On ne peut attacher qu’un seul événement à la fois.</li>
                <li>Si on définit deux fois <code>onclick</code>, le dernier écrase le premier.</li>
              </ul>
              <p class="mt-3">
                👉 C’est pour cela qu’on préfère <code>addEventListener</code>.
              </p>
            </div>
          </div>
        </section>

        <!-- Exemples interactifs -->
        <section class="mb-12">
          <div class="card bg-accent text-accent-content shadow-xl">
            <div class="card-body">
              <h2 class="card-title text-2xl">🧪 Exemples pratiques</h2>
              <p class="mb-4">Voici différents cas d’utilisation :</p>
              
              <!-- Exemple 1 -->
              <h3 class="font-bold text-lg mt-4">1. Changer le texte d’un bouton</h3>
              <button id="demoBtn" class="btn btn-neutral mt-2">Clique-moi</button>
              <pre class="mockup-code text-left mt-2">
                <code>const demoBtn = document.querySelector("#demoBtn");
demoBtn.addEventListener("click", () => {
  demoBtn.textContent = "Bravo 🎉 !";
});</code>
              </pre>

              <!-- Exemple 2 -->
              <h3 class="font-bold text-lg mt-6">2. Récupérer l’événement avec <code>event</code></h3>
              <input id="nameInput" type="text" placeholder="Tapez votre nom" class="input input-bordered mt-2" />
              <pre class="mockup-code text-left mt-2">
                <code>const input = document.querySelector("#nameInput");
input.addEventListener("keyup", (event) => {
  console.log("Touche pressée :", event.key);
  console.log("Valeur actuelle :", event.target.value);
});</code>
              </pre>

              <!-- Exemple 3 -->
              <h3 class="font-bold text-lg mt-6">3. Éviter le rechargement d’un formulaire</h3>
              <form id="demoForm" class="mt-3 space-y-2">
                <input type="text" placeholder="Message" class="input input-bordered w-full max-w-xs" />
                <button type="submit" class="btn btn-primary">Envoyer</button>
              </form>
              <pre class="mockup-code text-left mt-2">
                <code>const form = document.querySelector("#demoForm");
form.addEventListener("submit", (event) => {
  event.preventDefault(); // Empêche le rechargement
  console.log("Formulaire soumis !");
});</code>
              </pre>
            </div>
          </div>
        </section>
      </main>

      <!-- Footer -->
      <div id="footer-container"></div>
    </div>
  </div>
  
  <script type="module">
    // Exemple 1
    const demoBtn = document.querySelector("#demoBtn");
    demoBtn.addEventListener("click", () => {
      demoBtn.textContent = "Bravo 🎉 !";
    });

    // Exemple 2
    const input = document.querySelector("#nameInput");
    input.addEventListener("keyup", (event) => {
      console.log("Touche pressée :", event.key);
      console.log("Valeur actuelle :", event.target.value);
    });

    // Exemple 3
    const form = document.querySelector("#demoForm");
    form.addEventListener("submit", (event) => {
      event.preventDefault();
      console.log("Formulaire soumis !");
    });
  </script>
</body>
</html>
