<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Variables JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div id="navbar-container"></div>


        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div class="flex justify-center items-center min-h-screen bg-base-200">
            <div class="card card-compact bg-base-100 shadow-xl p-4 w-80">
              <h1 class="text-3xl font-bold text-center mb-4">R<PERSON><PERSON> Social (Classes)</h1>
        </main>

        <!-- Footer -->
        <div id="footer-container"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
//!---------------------------------------------------------------------------
//!----------------------- TP Réseau Social -----------------------------------
//!---------------------------------------------------------------------------
// Classe Utilisateur

// VERSION avec les try/catch dans les méthodes publier et rejoindreGroupe et dans la méthode ajouterCommentaire de la classe Publication
class Utilisateur {
    constructor(nom) {
        this.nom = nom;
        this.publications = [];
        this.groupes = [];
    }

    publier(contenu) {
        try {
            const motsInappropriés = ["fck", "fcked"];
            if (motsInappropriés.some(mot => contenu.includes(mot))) {
                throw new Error("Erreur : Contenu inapproprié détecté.");
            }
            const publication = new Publication(this, contenu);
            this.publications.push(publication);
            return publication;
        } catch (error) {
            console.error(`Publication échouée pour ${this.nom} : ${error.message}`);
        }
    }

    rejoindreGroupe(groupe) {
        try {
            if (!this.groupes.includes(groupe)) {
                groupe.ajouterMembre(this);
                this.groupes.push(groupe);
            }
        } catch (error) {
            console.error(`Erreur pour rejoindre le groupe ${groupe.nom} : ${error.message}`);
        }
    }
}

// Classe Publication
class Publication {
    constructor(auteur, contenu) {
        this.auteur = auteur;
        this.contenu = contenu;
        this.commentaires = [];
    }

    ajouterCommentaire(commentaire) {
        try {
            const motsInsultants = ["idiot", "stupide"];
            if (motsInsultants.some(mot => commentaire.contenu.includes(mot))) {
                throw new Error("Erreur : Commentaire insultant détecté.");
            }
            this.commentaires.push(commentaire);
        } catch (error) {
            console.error(`Ajout de commentaire échoué : ${error.message}`);
        }
    }
}

// Classe Commentaire
class Commentaire {
    constructor(auteur, contenu) {
        this.auteur = auteur;
        this.contenu = contenu;
    }
}

// Classe Groupe
class Groupe {
    constructor(nom, estPrivé = false) {
        this.nom = nom;
        this.membres = [];
        this.estPrivé = estPrivé;
    }

    ajouterMembre(utilisateur) {
        try {
            if (this.membres.includes(utilisateur)) {
                throw new Error("Cet utilisateur est déjà membre du groupe.");
            }
            this.membres.push(utilisateur);
        } catch (error) {
            console.error(`Ajout de membre échoué pour ${this.nom} : ${error.message}`);
        }
    }

    afficherContenu(utilisateur) {
        try {
            if (this.estPrivé && !this.membres.includes(utilisateur)) {
                throw new Error(`Erreur :${utilisateur.nom} Vous n'avez pas accès à ce groupe.`);
            }
            console.log(`Contenu du groupe ${this.nom}.`);
        } catch (error) {
            console.error(error.message);
        }
    }
}

// Tests
try {
    //! Creation d'utilisateurs
    const utilisateur1 = new Utilisateur("Alice");
    const utilisateur2 = new Utilisateur("Bob");
    const amandine38 = new Utilisateur("Amandine du 38");
    //! Création de groupes
    const groupePublic = new Groupe("Voyageurs", false);
    const groupePrivé = new Groupe("Investisseurs", true);

    //! Scénario FAIL : on essaie d'afficher  le contenu d'un groupe privé à Charlie
    // Mais charlie n'est pas membre de ce groupe donc erreur
    const justiceLeague = new Groupe("La Justice League", true);
    const charlie = new Utilisateur("Charlie");
    justiceLeague.afficherContenu(charlie); // Cela devrait déclencher l'erreur on affiche pas le contenu privé à charlie

    //! Scénario FAIL : Amandine est dans un groupe privé 
    // Mais elle essaie quand meme de rejoindre ce groupe donc erreur 
    // vous etes déjà dans ce groupe (ca évite les doublons)
    groupePrivé.ajouterMembre(amandine38);
    amandine38.rejoindreGroupe(groupePrivé);

    utilisateur1.rejoindreGroupe(groupePublic);
    utilisateur2.rejoindreGroupe(groupePrivé);

    const publication = utilisateur1.publier("Bienvenue  dans mon réseau social !");
    const publicationInvalide = utilisateur2.publier("Bienvenue  dans mon réseau social fcked!");
    if (publication) {
        console.log(`${utilisateur1.nom} a publié : "${publication.contenu}"`);
    }
    if (publicationInvalide) {
        console.log(`${utilisateur1.nom} a publié : "${publication.contenu}"`);
    }

    const commentaireValide = new Commentaire(utilisateur2, "Super publication !");
    if (publication) publication.ajouterCommentaire(commentaireValide);

    const commentaireInsultant = new Commentaire(utilisateur2, "C'est stupide !");
    if (publication) publication.ajouterCommentaire(commentaireInsultant);
} catch (error) {
    console.error(`Erreur globale : ${error.message}`);
}
    </script>
  </body>
</html>
