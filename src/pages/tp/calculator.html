<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Variables JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div id="navbar-container"></div>


        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div class="flex justify-center items-center min-h-screen bg-base-200">
            <div class="card card-compact bg-base-100 shadow-xl p-4 w-80">
              <h1 class="text-3xl font-bold text-center mb-4">Calculatrice</h1>
          
              <div class="card-body p-0">
                <input type="text" id="result" class="input input-bordered w-full text-2xl text-right mb-4" disabled>
          
                <div id="calculator" class="grid grid-cols-4 gap-2">
                  <button class="btn btn-primary">1</button>
                  <button class="btn btn-primary">2</button>
                  <button class="btn btn-primary">3</button>
                  <button class="btn btn-warning">+</button>
          
                  <button class="btn btn-primary">4</button>
                  <button class="btn btn-primary">5</button>
                  <button class="btn btn-primary">6</button>
                  <button class="btn btn-warning">-</button>
          
                  <button class="btn btn-primary">7</button>
                  <button class="btn btn-primary">8</button>
                  <button class="btn btn-primary">9</button>
                  <button class="btn btn-warning">*</button>
          
                  <button class="btn btn-error">C</button>
                  <button class="btn btn-primary">0</button>
                  <button class="btn btn-success">=</button>
                  <button class="btn btn-warning">/</button>
                </div>
              </div>
            </div>
          </div>
        </main>

        <!-- Footer -->
        <div id="footer-container"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
//!---------------------------------------------------------------------------
//!----------------------- TP Calculatrice -----------------------------------
//!---------------------------------------------------------------------------
let currentInput = '';
let operation = '';
let previousInput = '';

function appendNumber(number) {
    currentInput += number;
    updateDisplay();
}

function setOperation(op) {
    if (currentInput === '') return;
    if (previousInput !== '') {
        calculateResult();
    }
    operation = op;
    previousInput = currentInput;
    currentInput = '';
}

function calculateResult() {
    let result;
    const prev = parseFloat(previousInput);
    const current = parseFloat(currentInput);
    if (isNaN(prev) || isNaN(current)) return;
    switch (operation) {
        case '+':
            result = prev + current;
            break;
        case '-':
            result = prev - current;
            break;
        case '*':
            result = prev * current;
            break;
        case '/':
            result = prev / current;
            break;
        default:
            return;
    }
    currentInput = result.toString();
    operation = '';
    previousInput = '';
    updateDisplay();
}

function clearResult() {
    currentInput = '';
    previousInput = '';
    operation = '';
    updateDisplay();
}

function updateDisplay() {
    document.getElementById('result').value = currentInput;
}

// Ajout des écouteurs d'événements pour les boutons
document.querySelectorAll('.btn-primary, .btn-warning, .btn-error, .btn-success').forEach(button => {
    button.addEventListener('click', (event) => {
        const buttonText = event.target.innerText;
        if (buttonText === 'C') {
            clearResult();
        } else if (buttonText === '=') {
            calculateResult();
        } else if (['+', '-', '*', '/'].includes(buttonText)) {
            setOperation(buttonText);
        } else {
            appendNumber(buttonText);
        }
    });
});
    </script>
  </body>
</html>
