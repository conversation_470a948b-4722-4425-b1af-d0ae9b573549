<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>TP : Classes Compte Bancaires JavaScript - Jefff303.js</title>
  </head>
  <body class="min-h-screen">
    <div class="drawer min-h-screen">
      <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content flex flex-col min-h-screen">
        <!-- Navbar -->
        <div id="navbar-container"></div>


        <!-- Main Content -->
        <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div class="flex justify-center items-center min-h-screen bg-base-200">
            <div class="card card-compact bg-base-100 shadow-xl p-4 w-80">
              <h1 class="text-3xl font-bold text-center mb-4">TP : Classes Compte Bancaires</h1>
        </main>

        <!-- Footer -->
        <div id="footer-container"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="module">
//!---------------------------------------------------------------------------
//!----------------------- TP Classes Compte Bancaires------------------------
//!---------------------------------------------------------------------------
// Classe Utilisateur

class CompteBancaire {
    constructor(titulaire) {
        this.titulaire = titulaire;
        this.solde = 0;
    }
    // Ajoute un montant au solde
    crediter(montant) {
        this.solde += montant;
        console.log("Ajout de: " + montant + " pour: " + this.titulaire);
    }
    // Retirer un montant au solde
    retirer(montant) {
        try {
        if (this.solde < montant)
            throw (
            this.titulaire +
            ", retrait de: " +
            montant +
            " refusé avec solde: " +
            this.solde
            );
        this.solde -= montant;
        console.log("Retrait de: " + montant + " pour: " + this.titulaire);
        } catch (err) {
        console.log("----->" + err);
        }
    }
    virer(montant, membre) {
        console.log(
        "Virement de: " +
            montant +
            " de: " +
            this.titulaire +
            " vers: " +
            membre.titulaire
        );
        membre.crediter(montant);
        this.retirer(montant);
    }

    // Renvoie la description du compte
    decrire() {
        return "titulaire: " + this.titulaire + ", solde: " + this.solde;
    }
    }

    // Main, gère 3 comptes bancaires dans un tableau associatif
    const lesComptes = {
    Alex: new CompteBancaire("Alex"),
    Clovis: new CompteBancaire("Clovis"),
    Marco: new CompteBancaire("Marco"),
    };

    // lecture tableau associatif ou Objet["truc"]
    // Crédite et décrit chaque compte
    for (let key in lesComptes) {
      lesComptes[key].crediter(1000);
    }

    // un retrait de 100 par Alex
    lesComptes["Alex"].retirer(100);
    // un petit virement: Marco Vire 300 à Clovis
    lesComptes["Marco"].virer(300, lesComptes["Clovis"]);
    // un petit retrait incorrect (doit déclencher erreur custom) : 
    // Alex fait un retrait de 1200
    lesComptes["Alex"].retirer(1200);
    // bilan : faire une déscription de tous les comptes en console (ou DOM ?)
    for (let key in lesComptes){ 
      console.log(lesComptes[key].decrire());}
    </script>
  </body>
</html>
