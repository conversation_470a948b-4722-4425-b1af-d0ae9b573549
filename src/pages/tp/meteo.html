<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Variables JavaScript - Jefff303.js</title>
    
    <script src="https://kit.fontawesome.com/707f08755a.js" crossorigin="anonymous"></script>

</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar -->
            <div id="navbar-container"></div>


            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <h1 class="text-4xl font-bold mb-4 text-center">TP : DOM API Meteo</h1>
                <!-- Section de héros main call to action -->
                 <!-- Card Météo -->
                  <div class="card  w-96  mx-auto bg-base-100 shadow-xl">
                <div class="cardMeteo mx-auto" data-aos="slide-up" data-aos-duration="1000">
                    <h1 class="cardMeteo__titre">Météo du Jour</h1>
                    <i class="fa-solid fa-cloud-sun"></i>
                    <button id="btn">Charger</button>
                </div>
                </div>

            </main>

            <!-- Footer -->
            <div id="footer-container"></div>
        </div>
    </div>
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

    <script type="module" src="/src/main.js"></script>
    <script type="module">
        //!---------------------------------------------------------------------------
        //!----------------------- TP DOM API Meteo ----------------------------------
        //!---------------------------------------------------------------------------
        //Variable et Recup des éléments html
        let meteo;
        let infoMet;
        let button = document.querySelector("#btn");
        // let button = document.getElementsByTagName("button")[0];
        // let cardMeteo = document.getElementsByClassName("cardMeteo")[0];
        let cardMeteo = document.querySelector(".cardMeteo");


        //crée une div, avant le bouton, qui affichera les infos météos
        let info = document.createElement("div");
        info.style.height = "300px";
        info.style.width = "200px";
        info.style.margin = "16px 0";
        info.style.border = "3px solid grey";
        info.style.padding = "16px 12px 24px";
        cardMeteo.insertBefore(info, button);


        //FONCTIONS
        //ajoute une class au bouton qui va changer sa couleur en vert
        function bouton() {
            button.classList.add("button__cardMeteo");
        }

        //affiche les infos météos
        function addInfo() {
            cardMeteo.getElementsByTagName("div")[0].innerText = infoMet;
        }

        //récupère les infos météos dans une variable (affiché dans la div précédente de addInfo())
        function infoMeteo() {
            let tmax = meteo.fcst_day_0.tmax;
            let tmin = meteo.fcst_day_0.tmin;
            let tcurrent = meteo.current_condition.tmp;
            let condition = meteo.current_condition.condition;
            infoMet = `Aujourd'hui le temps est : ${condition}, et la température est de ${tcurrent}°C
    T° Max = ${tmax} - T° Min = ${tmin}`;
            console.log(infoMet);
        }


        //INTERACTION ET API
        //change la couleur du bouton en restant appuyé dessus
        button.addEventListener("mousedown", (event) => {
            button.style.backgroundColor = "orange";
        })

        //annule l'interaction précédente
        document.addEventListener("mouseup", (event) => {
            button.style.backgroundColor = "";
        })

        //demande les infos météos au service web, et exécute les fonctions permettant leur affichage
        button.addEventListener("click", (event) => {
            fetch("https://prevision-meteo.ch/services/json/toulouse")
                .then((response) => {
                    return response.json();
                })
                .then((value) => {
                    meteo = value;
                    console.log(value);
                    infoMeteo();
                    bouton();
                    addInfo();
                })
                .catch((error) => {
                    console.log(error);
                });
        })




    </script>
        <script>
            AOS.init();
          </script>
</body>

</html>