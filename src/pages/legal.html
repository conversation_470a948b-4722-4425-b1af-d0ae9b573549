<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/src/style.css">
    <title>Jefff303.js</title>
</head>

<body class="min-h-screen">
    <div class="drawer min-h-screen">
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Navbar Container - sera rempli par JavaScript -->
            <div id="navbar-container"></div>

            <!-- Main Content -->
            <main class="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                <div class="max-w-4xl mx-auto">
                    <!-- Header -->
                    <div class="text-center mb-8">
                        <h1 class="text-4xl font-bold text-primary mb-4">Mentions Légales</h1>
                        <p class="text-lg text-base-content/70">Informations légales et conditions d'utilisation</p>
                    </div>

                    <!-- Content Card -->
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <!-- Éditeur du site -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    Éditeur du site
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <p class="mb-2"><strong>Nom :</strong> Jefff303.js</p>
                                    <p class="mb-2"><strong>Type :</strong> Site éducatif - Cours de JavaScript</p>
                                    <p class="mb-2"><strong>Email :</strong> <EMAIL></p>
                                    <p><strong>Statut :</strong> Projet éducatif open source</p>
                                </div>
                            </section>

                            <!-- Hébergement -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                                    </svg>
                                    Hébergement
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <p class="mb-2"><strong>Hébergeur :</strong> GitHub Pages</p>
                                    <p class="mb-2"><strong>Adresse :</strong> GitHub, Inc. - 88 Colin P Kelly Jr St, San Francisco, CA 94107, États-Unis</p>
                                    <p><strong>Site web :</strong> <a href="https://pages.github.com" class="link link-primary" target="_blank">https://pages.github.com</a></p>
                                </div>
                            </section>

                            <!-- Propriété intellectuelle -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    Propriété intellectuelle
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg space-y-3">
                                    <p>Le contenu de ce site (cours, exercices, exemples de code) est mis à disposition sous licence open source.</p>
                                    <div class="alert alert-info">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>Ce projet est open source et disponible sur GitHub pour l'apprentissage et la contribution.</span>
                                    </div>
                                </div>
                            </section>

                            <!-- Données personnelles -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    Protection des données
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg space-y-3">
                                    <p>Ce site ne collecte aucune donnée personnelle des utilisateurs.</p>
                                    <ul class="list-disc list-inside space-y-1 text-sm">
                                        <li>Aucun cookie de tracking n'est utilisé</li>
                                        <li>Aucune donnée n'est stockée côté serveur</li>
                                        <li>Le stockage local du navigateur peut être utilisé pour sauvegarder la progression</li>
                                        <li>Aucune donnée n'est partagée avec des tiers</li>
                                    </ul>
                                </div>
                            </section>

                            <!-- Responsabilité -->
                            <section class="mb-8">
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    Limitation de responsabilité
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg space-y-3">
                                    <p>Ce site est fourni à des fins éducatives uniquement.</p>
                                    <div class="alert alert-warning">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <span>L'éditeur ne peut être tenu responsable des dommages directs ou indirects résultant de l'utilisation de ce site.</span>
                                    </div>
                                </div>
                            </section>

                            <!-- Contact -->
                            <section>
                                <h2 class="text-2xl font-semibold text-primary mb-4 flex items-center">
                                    <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    Contact
                                </h2>
                                <div class="bg-base-200 p-4 rounded-lg">
                                    <p class="mb-3">Pour toute question concernant ces mentions légales ou le contenu du site :</p>
                                    <div class="flex flex-col sm:flex-row gap-4">
                                        <a href="mailto:<EMAIL>" class="btn btn-primary btn-outline">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                            Nous contacter
                                        </a>
                                        <a href="https://github.com/jefff303js" target="_blank" class="btn btn-secondary btn-outline">
                                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                            </svg>
                                            GitHub
                                        </a>
                                    </div>
                                </div>
                            </section>

                            <!-- Date de mise à jour -->
                            <div class="text-center mt-8 pt-6 border-t border-base-300">
                                <p class="text-sm text-base-content/60">
                                    Dernière mise à jour : <span class="font-medium">Décembre 2024</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <!-- Footer Container - sera rempli par JavaScript -->
            <div id="footer-container"></div>
        </div>
    </div>
    <!-- Pb ptet a adapter si page sous dossier/src/pages/exo/dom-events.html -->
    <script type="module" src="/src/main.js"></script>
</body>

</html>