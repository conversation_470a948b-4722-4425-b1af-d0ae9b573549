# TODO LIST

- [ ] Page : A propos (Une page qui vous présente de manière stylée #lameilleureversiondemoimeme), template (strict minimum DaisyUI),les datas sont en JS of course
- [ ] Un systeme de consigne pour les exo et TP
- [ ] Page Skills dynamique service (une page qui liste les skills (cf tab competences cda titre) avec des liens vers les leçons correspondantes)
- [ ] Outil pour Jefff : Une page De group Generator (équitable + persistant + import clipBoard)
- [ ] Outil utilitaire une fonction qui affiche ce qu'on veut dans une page (on lui donne un id en parametre)
- [ ] Lien lessons page skills (skillsDataService)
- [ ] un systeme de timer pour les exo et tp
- [ ] Page Tools (lister des outils de dev pour JS)
- [ ] Page Veille tech
- [ ] Page Register avec Regex et Dompurify
- [ ] Intro Testing avec cypress
- [ ] Page lesson sur les frameworks (CSS, JS, Testing)
- [ ] Page lesson sur XSS importance Sanitize, Dompurify,innerHTML vs textContent
- [ ] Outils systeme de vote poll
- [ ] integrer lib vanta.js
- [ ] outils de tier list
- [ ] outils d'auto eval pour progression

## Des Idees de pages ou Features

- [ ] README.md : expliquer bien le projet, lien stylé github, stackblitz, codepen
- [ ] Améliorer la Page (/lessons/setup): Setup Vite (la page explique comment démmarrer un projet JS/TS avec Vite parce que c est cool) avec explication des fichiers et des dossiers spécifiques (package.json, vite.config.js, node_modules, dist, src, index.html, main.js, style.css, etc.)
- [ ] Page Padero Favor de Responder (XP Social)
- [ ] Faire pieuvre

## Lundi 1/09/2025 parcours CDA (Code Sportifs)

- [x] Présentation Javascript
  Stack : NPM, Vite, Tailwind, DaisyUI, DomPurify.
- [X] Rev : Variables
- [X] Rev : Fonctions
- [X] DOM : Events

## Mardi 2/09/2025 parcours CDA (Code Sportifs)

- [x] Présentation Nouveau Setup
  Stack : NPM, Vite, Tailwind, DaisyUI, DomPurify.
- [x] Question Random Dom Selectors
- [X] Rev : JSDoc l'art de bien faire des commentaires
- [X] Rev : Fonctions
- [X] DOM : Events
- [x] Navbar : Rendre Dynamique le nom du site
- [x] Footer : Rendre Dynamique la date (année) du copyright

## Mercredi 3/09/2025 parcours CDA (Code Sportifs)

- [x] Variables Environnement
- [x] Mettre sur Github
- [x] Outil pour Jefff : Une page compteur de mensonge (persistant)
- [x] Installer des librairies
- [x] Rev : Boucles
- [x] Page : Team Dynamique (on a un tableau d'objet en JS on doit l'afficher dans le template)

## Jeudi 4/09/2025 parcours CDA (Code Sportifs)

- [x] Gestion erreurs
- [x] Service Imports export
- [x] TP Service : Page Progression (Timeline)
- [x] API

## Vendredi 5/09/2025 parcours CDA (Code Sportifs)

- [x] Classes
- [x] Exercices Classes : easy IMC, Moyen Compte Bancaires, Heavy Magasin ou Bibliothèque
- [x] DOM : Attack regex
- [x] Page Contact (Un formulaire de contact sécurisé avec Regex)
- [x] Page Login et Register (Un formulaire de login et register sécurisé avec Regex)
- [x] Introduction Typescript

## Lundi 8/09/2025 parcours CDA (Code Sportifs)

- [ ] Nouveau setup,install cypress
- [ ] install chartjs
- [ ] Classes
- [ ] Correction : Exercices Classes : easy IMC, Moyen Compte Bancaires, Heavy Magasin ou Bibliothèque
- [ ] DOM : Attack regex
- [ ] DOM : Attack regex (Correction - Medley pour login et register)
- [ ] Page Contact (Un formulaire de contact sécurisé avec Regex)
- [ ] Page Login et Register (Un formulaire de login et register sécurisé avec Regex)
- [ ] Introduction Typescript
